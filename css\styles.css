/* ---------------------------------------------------------
    * Name: 
    * Version: 1.0.0
    * Author: Themesflat
    * Author URI: http://themesflat.com 

	* Abstracts variable

    * Reset css styles

    * Components

    * section

    * dashboard

    * Responsive
 ------------------------------------------------------------------------------ */
/*--------- Abstracts variable ---------- */
:root {
  --Primary:#d2a452;
  --Secondary: #1f1e1e;
  --White: #ffffff;
  --Black:#000000;
  --text:#171412;
  --text-2:#151515;
  --text-3:#ededed;
  --body-text: #666666;
  --Text-top-bar:#dedede ;
  --sort-text:#c28e32;
  --Border:#807168 ;
  --Line:#e1e2e3;
  --Bg-1: #3a3939;
  --Bg-2:#f1f1f1;
}

/*---------- Reset css styles ----------- */
/* Reset Browsers
-------------------------------------------------------------- */
html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
  font: inherit;
  vertical-align: baseline;
  font-family: inherit;
  font-size: 100%;
  font-style: inherit;
  font-weight: inherit;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}

/* html {
  margin-right: 0 !important;
  font-size: 62.5%;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  scroll-behavior: smooth;
} */

*,
*:before,
*:after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

body {
  background: var(--White);
  line-height: 1;
  padding: 0 !important;
}
body::-webkit-scrollbar {
  width: 0px;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
main,
nav,
section {
  display: block;
}

ol,
ul {
  list-style: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

caption,
th,
td {
  font-weight: normal;
  text-align: left;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
  content: "";
}

blockquote,
q {
  quotes: none;
}

a img {
  border: 0;
}

img {
  max-width: 100%;
  height: auto;
  vertical-align: middle;
}

select {
  max-width: 100%;
}

/* General
-------------------------------------------------------------- */
body,
button,
input,
select,
textarea {
  font-family: "DM Sans", sans-serif;
  font-size: 15px;
  font-weight: 400;
  color: var(--body-text);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  overflow-x: hidden;
  overflow-y: auto;
}

p {
  font-weight: 400;
  font-size: 15px;
  line-height: 25px;
}

strong,
b,
cite {
  font-weight: bold;
}

dfn,
cite,
em,
i,
blockquote {
  font-style: italic;
}

abbr,
acronym {
  border-bottom: 1px dotted #e0e0e0;
  cursor: help;
}

.btn-link:focus,
.btn-link:hover,
mark,
ins {
  text-decoration: none;
}

sup,
sub {
  font-size: 75%;
  height: 0;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

small {
  font-size: 75%;
}

big {
  font-size: 125%;
}

address {
  font-style: italic;
  margin: 0 0 20px;
}

code,
kbd,
tt,
var,
samp,
pre {
  margin: 20px 0;
  padding: 4px 12px;
  background: #f5f5f5;
  border: 1px solid #e0e0e0;
  overflow-x: auto;
  -webkit-hyphens: none;
  -moz-hyphens: none;
  hyphens: none;
  border-radius: 0;
  height: auto;
}

svg,
svg path {
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

/* Elements
-------------------------------------------------------------- */
html {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

*,
*:before,
*:after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

hr {
  margin-bottom: 20px;
  border: dashed 1px #ccc;
}

/* List */
ul,
ol {
  padding: 0;
}

ul {
  list-style: disc;
}

ol {
  list-style: decimal;
}

li > ul,
li > ol {
  margin-bottom: 0;
}

li {
  list-style: none;
}

ul li,
ol li {
  padding: 0;
}

dl,
dd {
  margin: 0 0 20px;
}

dt {
  font-weight: bold;
}

del,
.disable {
  text-decoration: line-through;
  filter: alpha(opacity=50);
  opacity: 0.5;
}

/* Table */
table,
th,
td {
  border: 1px solid #343444;
}

table {
  border-collapse: separate;
  border-spacing: 0;
  border-width: 1px 0 0 1px;
  margin: 0 0 30px;
  table-layout: fixed;
  width: 100%;
}

caption,
th,
td {
  font-weight: normal;
  text-align: left;
}

th {
  border-width: 0 1px 1px 0;
  font-weight: bold;
}

td {
  border-width: 0 1px 1px 0;
}

th,
td {
  padding: 8px 12px;
}

/* Media */
embed,
object,
video {
  margin-bottom: 20px;
  max-width: 100%;
  vertical-align: middle;
}

p > embed,
p > iframe,
p > object,
p > video {
  margin-bottom: 0;
}

/* Forms
-------------------------------------------------------------- */
/* Fixes */
button,
input {
  line-height: normal;
}

button,
input,
select,
textarea {
  font-size: 100%;
  line-height: inherit;
  margin: 0;
  vertical-align: baseline;
}

textarea {
  overflow: auto;
  /* Removes default vertical scrollbar in IE6/7/8/9 */
  vertical-align: top;
  /* Improves readability and alignment in all browsers */
}

input[type=checkbox] {
  display: inline;
}

button,
input[type=button],
input[type=reset],
input[type=submit] {
  line-height: 1;
  cursor: pointer;
  -webkit-appearance: button;
  border: 0;
}

input[type=checkbox],
input[type=radio] {
  padding: 0;
  width: 20px;
  height: 20px;
  border-radius: 6px;
  background: rgba(0, 0, 0, 0);
  cursor: pointer;
  vertical-align: sub;
  /* Addresses excess padding in IE8/9 */
}

input[type=search] {
  -webkit-appearance: textfield;
  /* Addresses appearance set to searchfield in S5, Chrome */
}

input[type=search]::-webkit-search-decoration {
  /* Corrects inner padding displayed oddly in S5, Chrome on OSX */
  -webkit-appearance: none;
}

button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0;
}

/* Remove chrome yellow autofill */
input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0px 1000px #f7f7f7 inset;
}

/* Reset search styling */
input[type=search] {
  outline: 0;
}

input[type=search]::-webkit-search-decoration,
input[type=search]::-webkit-search-cancel-button,
input[type=search]::-webkit-search-results-button,
input[type=search]::-webkit-search-results-decoration {
  display: none;
}

/* Placeholder color */
::-webkit-input-placeholder {
  color: #171412;
}

::-moz-placeholder {
  color: #171412;
  opacity: 1;
}

/* Since FF19 lowers the opacity of the placeholder by default */
:-ms-input-placeholder {
  color: #171412;
}

/* Typography
-------------------------------------------------------------- */
h1,
.h1,
h2,
.h2,
h3,
.h3,
h4,
.h4,
h5,
.h5,
h6,
.h6 {
  font-family: "CormorantInfant", sans-serif;
  margin: 0;
  text-rendering: optimizeLegibility;
}

h1,
.h1 {
  font-size: 65px;
  line-height: 70px;
}

h2,
.h2 {
  font-size: 35px;
  line-height: 45px;
}

h3,
.h3 {
  font-size: 30px;
  line-height: 28px;
}

h4,
.h4 {
  font-size: 22px;
  line-height: 30px;
}

h5,
.h5 {
  font-size: 20px;
  line-height: 30px;
}

h6,
.h6 {
  font-size: 17px;
  line-height: 26px;
}

.text-1 {
  font-size: 25px;
  line-height: 32px;
}

.text-2 {
  font-size: 20px;
  line-height: 32px;
}

.text-3 {
  font-size: 23px;
  line-height: 33px;
}

.text-4 {
  font-size: 45px;
  line-height: 55px;
}

.fw-4 {
  font-weight: 400;
}

.fw-5 {
  font-weight: 500;
}

.fw-6 {
  font-weight: 600;
}

.fw-7 {
  font-weight: 700;
}

/* link style
-------------------------------------------------------------- */
a {
  text-decoration: none;
  color: var(--text);
  cursor: pointer;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
a:hover, a:focus {
  color: var(--Primary);
  outline: 0;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

/* tf-container
-------------------------------------------------------------- */
.tf-container {
  position: relative;
  margin-left: auto;
  margin-right: auto;
  width: 1200px;
  padding-right: 15px;
  padding-left: 15px;
  max-width: 100%;
}
.tf-container .row {
  margin-left: -15px !important;
  margin-right: -15px !important;
}
.tf-container .row > * {
  padding-left: 15px !important;
  padding-right: 15px !important;
}
.tf-container.full {
  width: 100%;
}
.tf-container.w-1550 {
  width: 1550px;
}

/* Extra classes
-------------------------------------------------------------- */
#wrapper {
  position: relative;
  overflow: hidden;
  max-width: 100%;
  height: 100%;
}

.hidden {
  display: none;
}

.block {
  display: block;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed !important;
}

.position-unset {
  position: unset !important;
}

.z-5 {
  z-index: 5;
}

.flex {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
}

.justify-start {
  justify-content: start !important;
}

.justify-center {
  justify-content: center !important;
}

.justify-end {
  justify-content: flex-end !important;
}

.justify-between {
  justify-content: space-between !important;
}

.items-center {
  align-items: center !important;
}

.flex-wrap {
  flex-wrap: wrap;
}

.font-cerebri {
  font-family: "DM Sans", sans-serif !important;
}

.line-clamp-1 {
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  display: -webkit-box;
}

.line-clamp-2 {
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  display: -webkit-box;
}

.line-clamp-4 {
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
  display: -webkit-box;
}

.pt-0 {
  padding-top: 0 !important;
}

.pb-0 {
  padding-bottom: 0 !important;
}

.px-30 {
  padding: 0 30px !important;
}

.mb-9 {
  margin-bottom: 9px;
}

.mb-30 {
  margin-bottom: 30px !important;
}

.gap-10 {
  gap: 10px;
}

.overflow-x-auto {
  overflow-x: auto;
}
.overflow-x-auto::-webkit-scrollbar {
  height: 3px;
}
.overflow-x-auto::-webkit-scrollbar-thumb {
  background: var(--Text-top-bar);
  border-radius: 999px;
}

.overflow-y-auto {
  overflow-y: auto;
}
.overflow-y-auto::-webkit-scrollbar {
  width: 3px;
}
.overflow-y-auto::-webkit-scrollbar-thumb {
  background: var(--Border);
  border-radius: 999px;
}

/*------------ Components ---------------- */
/*------------ header ---------------- */
/*top-bar
-------------------------------------------------------------------*/
.tf-top-bar {
  padding: 14px 0;
  background-color: var(--Secondary);
}
.tf-top-bar .wrap-top {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  justify-content: space-between;
  gap: 10px;
}
.tf-top-bar .wrap-top .list-info {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  gap: 10px 44px;
  flex-wrap: wrap;
}
.tf-top-bar .wrap-top .list-info li {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  gap: 19px;
  font-size: 15px;
  line-height: 28px;
  color: var(--Text-top-bar);
  letter-spacing: 0.1px;
  transition: all 0.3s ease-in;
}
.tf-top-bar .wrap-top .list-info li i {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 35px;
  width: 35px;
  border-radius: 50%;
  background-color: var(--Bg-1);
  flex: none;
}
.tf-top-bar .wrap-top .list-sosial {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: start;
  gap: 25px;
}
.tf-top-bar .wrap-top .list-sosial li {
  font-size: 15px;
  line-height: 28px;
  color: var(--White);
  transition: all 0.3s ease-in;
  opacity: 50%;
}
.tf-top-bar .wrap-top li:hover {
  color: var(--Primary);
  cursor: pointer;
}

.list-sosial {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: start;
  gap: 25px;
}
.list-sosial li {
  font-size: 15px;
  line-height: 28px;
  color: var(--White);
  transition: all 0.3s ease-in;
  opacity: 50%;
}

/*Header-main
--------------------------------------------------------------------*/
#header-main .header-inner {
  z-index: 99;
  width: 100%;
  position: absolute;
}
#header-main .header-inner-wrap {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid rgba(255, 255, 255, 0.25);
}
#header-main .header-inner-wrap .header-left {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  gap: 121px;
}
#header-main .header-inner-wrap .header-left .navigation > li {
  position: relative;
}
#header-main .header-inner-wrap .header-left .navigation > li > a {
  position: relative;
}
#header-main .header-inner-wrap .header-left .navigation > li > a::before {
  position: absolute;
  content: "";
  bottom: -39px;
  left: auto;
  right: 0;
  width: 0;
  height: 3px;
  background-color: #c6ac83;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
#header-main .header-inner-wrap .header-left .navigation > li:hover a::before {
  width: 100%;
  left: 1px;
  right: auto;
}
#header-main .header-inner-wrap .header-right {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: start;
  gap: 73px;
}
#header-main .header-inner-wrap .header-right .wrap-header-icons {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  gap: 55px;
}
#header-main .header-inner-wrap .header-right .wrap-header-icons .header-search {
  position: relative;
}
#header-main .header-inner-wrap .header-right .wrap-header-icons .header-search:hover .show-search {
  color: var(--Primary);
}
#header-main .header-inner-wrap .header-right .wrap-header-icons .header-search .show-search {
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  color: var(--White);
}
#header-main .header-inner-wrap .header-right .wrap-header-icons .header-search .show-search i {
  line-height: 98px;
}
#header-main .header-inner-wrap .header-right .wrap-header-icons .header-search .show-search.active i::before {
  content: "\e931";
  font-family: "icomoon";
  height: 30px;
  width: 30px;
}
#header-main .header-inner-wrap .header-right .wrap-header-icons .header-search .top-search {
  position: absolute;
  top: 105%;
  right: 0;
  min-width: 240px;
  display: none;
  box-shadow: 0px 7px 5px 0px rgba(195, 162, 124, 0.2);
  -webkit-transition: all 0.5s ease;
  -moz-transition: all 0.5s ease;
  -ms-transition: all 0.5s ease;
  -o-transition: all 0.5s ease;
  transition: all 0.5s ease;
}
#header-main .header-inner-wrap .header-right .wrap-header-icons .header-search .top-search.active {
  display: block;
}
#header-main .header-inner-wrap .header-right .wrap-header-icons .header-cart {
  position: relative;
  cursor: pointer;
}
#header-main .header-inner-wrap .header-right .wrap-header-icons .header-cart::after {
  content: "";
  height: 80px;
  width: 59px;
  top: 0;
  left: -20px;
  display: block;
  position: absolute;
}
#header-main .header-inner-wrap .header-right .wrap-header-icons .header-cart .show-cart {
  color: var(--White);
  position: relative;
}
#header-main .header-inner-wrap .header-right .wrap-header-icons .header-cart:hover .show-cart {
  color: var(--Primary);
}
#header-main .header-inner-wrap .header-right .wrap-header-icons .header-cart:hover .bag-box {
  visibility: visible;
  opacity: 1;
}
#header-main .header-inner-wrap .header-right .wrap-header-icons .header-cart .bag-box {
  position: absolute;
  top: 105%;
  right: 0;
  background-color: #fff;
  box-shadow: 0px 0px 5px rgba(195, 162, 124, 0.2);
  width: 320px;
  padding: 20px;
  border-radius: 10px;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
#header-main .header-inner-wrap .header-right .wrap-header-icons .header-cart::before {
  position: absolute;
  content: "";
  top: 26px;
  left: -27px;
  width: 1px;
  height: 49px;
  background-color: rgba(255, 255, 255, 0.12);
}
#header-main .header-inner-wrap .header-right .wrap-header-icons i {
  font-size: 20px;
  font-weight: 500;
  line-height: 98px;
}
#header-main .header-inner-wrap .header-right .header-contact {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 22px;
}
#header-main .header-inner-wrap .header-right .header-contact .icon {
  height: 51px;
  width: 51px;
  background-color: var(--Primary);
  color: white;
  border-radius: 50%;
  text-align: center;
}
#header-main .header-inner-wrap .header-right .header-contact .icon i {
  font-size: 20px;
  line-height: 51px;
}
#header-main .header-inner-wrap .header-right .header-contact .content {
  color: var(--White);
}
#header-main .header-inner-wrap .header-right .header-contact .content .number {
  font-size: 22px;
  line-height: 30px;
  font-weight: 700;
}
#header-main .header-inner-wrap .header-right .mobile-button {
  display: none;
  font-size: 30px;
  color: var(--White);
  cursor: pointer;
}
#header-main .navigation {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: start;
  gap: 43px;
}
#header-main .navigation li {
  cursor: pointer;
  font-family: "CormorantInfant", sans-serif;
  font-size: 17px;
  line-height: 98px;
  font-weight: 500;
  letter-spacing: -0.2px;
}
#header-main .navigation li a {
  color: var(--White);
}
#header-main .navigation li:hover a {
  color: var(--Primary);
}
#header-main .navigation li .sub-menu {
  position: absolute;
  background: #fff;
  min-width: 220px;
  left: 0%;
  top: 105%;
  border-radius: 4px;
  box-shadow: 0px 0px 5px rgba(195, 162, 124, 0.2);
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.5s ease;
  -moz-transition: all 0.5s ease;
  -ms-transition: all 0.5s ease;
  -o-transition: all 0.5s ease;
  transition: all 0.5s ease;
}
#header-main .navigation li .sub-menu .current a {
  color: var(--Primary);
}
#header-main .navigation li .sub-menu li {
  padding: 10px 20px;
  line-height: 28px;
  font-weight: 500;
}
#header-main .navigation li .sub-menu li a {
  color: var(--text);
}
#header-main .navigation li .sub-menu li a:hover {
  color: var(--Primary);
}
#header-main .navigation li .sub-menu li:not(:last-child) {
  border-bottom: 1px solid #d3cece;
}
#header-main .navigation li:hover .sub-menu {
  opacity: 1;
  visibility: visible;
}

.header.is-fixed .header-inner {
  position: fixed !important;
  padding: 0;
  top: -100%;
  left: 0;
  width: 100%;
  z-index: 9999999999;
  border: none;
  background-color: var(--Secondary);
  opacity: 1;
  -webkit-transition: all 0.5s ease;
  -moz-transition: all 0.5s ease;
  -ms-transition: all 0.5s ease;
  -o-transition: all 0.5s ease;
  transition: all 0.5s ease;
}
.header.is-fixed .header-inner .header-inner-wrap {
  border-bottom: none !important;
}
.header.is-fixed.is-small .header-inner {
  top: 0;
  margin-top: 0px;
  width: 100%;
}

.mobile-nav-wrap .overlay-mobile-nav {
  content: "";
  position: fixed;
  left: 0;
  top: 0;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.8) 100%);
  width: 100%;
  height: 100%;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.mobile-nav-wrap .inner-mobile-nav {
  width: 320px;
  height: 100%;
  top: 0;
  left: -340px;
  position: fixed;
  background-color: var(--Black);
  z-index: 999999;
  overflow-y: auto;
  -webkit-transition: all 0.5s ease;
  -moz-transition: all 0.5s ease;
  -ms-transition: all 0.5s ease;
  -o-transition: all 0.5s ease;
  transition: all 0.5s ease;
}
.mobile-nav-wrap.active .overlay-mobile-nav {
  opacity: 1;
  visibility: visible;
}
.mobile-nav-wrap.active .inner-mobile-nav {
  left: 0;
}

.mobile-nav-wrap .inner-mobile-nav::-webkit-scrollbar {
  width: 0px;
}

.inner-mobile-nav {
  padding: 15px;
  position: relative;
}
.inner-mobile-nav .top-nav-mobile {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.inner-mobile-nav .top-nav-mobile #mobile-logo_header {
  width: 150px;
}
.inner-mobile-nav .top-nav-mobile .mobile-nav-close {
  cursor: pointer;
  width: 30px;
  height: 30px;
  background-color: var(--Bg-1);
  border-radius: 50%;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: center;
}
.inner-mobile-nav #menu-mobile-menu {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  margin-top: 15px;
  border-top: 1px solid rgba(255, 255, 255, 0.08);
  padding-top: 0px;
}
.inner-mobile-nav #menu-mobile-menu > li {
  position: relative;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  padding: 15px 0px;
  cursor: pointer;
}
.inner-mobile-nav #menu-mobile-menu > li .sub-menu-mobile {
  display: none;
  text-align: start;
  overflow: hidden;
}
.inner-mobile-nav #menu-mobile-menu > li .sub-menu-mobile li {
  padding-left: 15px;
  padding: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  cursor: pointer;
}
.inner-mobile-nav #menu-mobile-menu > li .sub-menu-mobile li a {
  font-family: "CormorantInfant", sans-serif;
  font-size: 16px;
  line-height: 22px;
  font-weight: 500;
}
.inner-mobile-nav #menu-mobile-menu li a {
  font-family: "CormorantInfant";
  font-size: 16px;
  line-height: 22px;
  font-weight: 500;
  text-transform: uppercase;
  color: #fff;
}
.inner-mobile-nav #menu-mobile-menu li.menu-item-has-children-mobile > a::after {
  content: "\e92d";
  position: absolute;
  font-family: "icomoon";
  font-weight: 300;
  position: absolute;
  font-size: 20px;
  right: 0;
  top: 17px;
  line-height: 16px;
  -webkit-transition: all 0.5s ease;
  -moz-transition: all 0.5s ease;
  -ms-transition: all 0.5s ease;
  -o-transition: all 0.5s ease;
  transition: all 0.5s ease;
}
.inner-mobile-nav #menu-mobile-menu li.active a::after {
  transform: rotate(-180deg);
}

#menu-mobile-menu > li .sub-menu-mobile li:last-child,
#menu-mobile-menu > li:last-child {
  border: none;
}

#menu-mobile-menu li.current-menu-item > a,
#menu-mobile-menu li a:hover,
#menu-mobile-menu li.current-item a {
  color: var(--Primary);
}

/*------------ footer ---------------- */
#footer .wrap-footer {
  background-color: var(--Secondary);
  position: relative;
}
#footer .wrap-footer .footer-body {
  padding-top: 126px;
  padding-bottom: 37px;
  position: relative;
  z-index: 5;
}
#footer .wrap-footer .footer-body .footer-info {
  margin-bottom: 30px;
}
#footer .wrap-footer .footer-body .footer-info .footer-logo {
  margin-bottom: 27px;
  margin-top: 4px;
}
#footer .wrap-footer .footer-body .footer-info .text {
  font-size: 15px;
  color: var(--Text-top-bar);
  margin-bottom: 47px;
  letter-spacing: 0.2px;
  padding-right: 34px;
}
#footer .wrap-footer .footer-body .title {
  margin-bottom: 56px;
  position: relative;
  color: var(--White);
  letter-spacing: 1.3px;
}
#footer .wrap-footer .footer-body .title::before {
  position: absolute;
  content: "";
  height: 2px;
  width: 73px;
  background-color: var(--sort-text);
  bottom: -17px;
  left: 0;
}
#footer .wrap-footer .footer-bottom {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  border-top: 1px solid #3c3b3b;
  padding-top: 30px;
  padding-bottom: 30px;
  gap: 20px;
  position: relative;
  z-index: 5;
}
#footer .wrap-footer .footer-bottom p {
  color: var(--Primary);
  letter-spacing: 0.4px;
  text-align: center;
}
#footer .wrap-footer .footer-bottom .content-right {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 29px;
}
#footer .wrap-footer .footer-bottom .content-right li a {
  color: var(--White);
}
#footer .wrap-footer .footer-bottom .content-right li:hover a {
  color: var(--Primary);
}
#footer .wrap-footer .footer-menu-list {
  margin-bottom: 30px;
}
#footer .wrap-footer .footer-menu-list ul {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  gap: 10px;
}
#footer .wrap-footer .footer-menu-list ul li {
  color: var(--text-3);
  line-height: 25px;
  letter-spacing: 0.3px;
}
#footer .wrap-footer .footer-menu-list.time .title {
  margin-left: -5px;
}
#footer .wrap-footer .footer-menu-list.time .title::before {
  left: 5px;
}
#footer .wrap-footer .footer-menu-list.latest-new p {
  margin-bottom: 27px;
  color: var(--text-3);
  letter-spacing: 0.4px;
}
#footer .wrap-footer .footer-menu-list.latest-new .form-send-email input {
  background-color: transparent;
  border: 1px solid #575656;
  color: var(--White);
}
#footer .wrap-footer .footer-menu-list.latest-new .form-send-email input::placeholder {
  color: var(--White);
}
#footer .wrap-footer .footer-menu-list.latest-new .form-send-email .button-submit {
  top: 25%;
  right: 18px;
}
#footer .wrap-footer .footer-menu-list.latest-new .form-send-email .button-submit img {
  max-width: 83%;
}
#footer .wrap-footer .footer-menu-list.latest-new .form-send-email .checkbox-item {
  margin-top: 33px;
  color: var(--text-3);
}
#footer .wrap-footer .footer-menu-list.location .title {
  margin-left: 3px;
}
#footer .wrap-footer .footer-menu-list.location .title::before {
  left: 4px;
}
#footer .wrap-footer .footer-menu-list.location ul {
  gap: 18px;
}
#footer .wrap-footer .footer-menu-list.location ul li .text {
  line-height: 28px;
  padding-left: 15px;
  letter-spacing: 0.4px;
}
#footer .wrap-footer .footer-menu-list.location ul li .text span {
  color: var(--sort-text);
  position: relative;
  display: inline-block;
  margin-bottom: 6px;
}
#footer .wrap-footer .footer-menu-list.location ul li .text span::before {
  position: absolute;
  content: "";
  height: 5px;
  width: 5px;
  background-color: var(--sort-text);
  left: -15px;
  top: 11px;
  border-radius: 50%;
}
#footer .wrap-footer .item {
  position: absolute;
  bottom: 49px;
}

/*------------ tabs ---------------- */
.widget-tabs .widget-menu-tab {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  justify-content: center;
  gap: 46px;
  letter-spacing: 2px;
}
.widget-tabs .widget-menu-tab .item-title {
  font-size: 17px;
  line-height: 29px;
  color: var(--text);
  font-weight: 700;
  white-space: nowrap;
  cursor: pointer;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.widget-tabs .widget-menu-tab .item-title.active, .widget-tabs .widget-menu-tab .item-title:hover {
  color: var(--Primary);
}
.widget-tabs.style-2 .widget-menu-tab {
  padding-bottom: 22px;
  gap: 50px;
  border-bottom: 1px solid var(--Text-top-bar);
  justify-content: start;
  letter-spacing: 1.5px;
  margin-bottom: 34px;
}
.widget-tabs.style-2 .widget-menu-tab .item-title {
  position: relative;
  font-family: "CormorantInfant", sans-serif;
}
.widget-tabs.style-2 .widget-menu-tab .item-title::before {
  position: absolute;
  content: "";
  bottom: -22px;
  left: auto;
  right: 0;
  width: 0;
  height: 3px;
  background-color: var(--Primary);
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.widget-tabs.style-2 .widget-menu-tab .item-title.active, .widget-tabs.style-2 .widget-menu-tab .item-title:hover {
  color: var(--Primary);
}
.widget-tabs.style-2 .widget-menu-tab .item-title.active::before, .widget-tabs.style-2 .widget-menu-tab .item-title:hover::before {
  width: 100%;
  left: 0;
}

/*------------ button ---------------- */
.tf-btn-default {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: start;
  gap: 7px;
  font-size: 13px;
  line-height: 25px;
  font-weight: 500;
  color: var(--Secondary);
}

.tf-btn {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: center;
  width: max-content;
  text-transform: uppercase;
  color: var(--White);
  font-size: 13px;
  letter-spacing: 1.6px;
  font-weight: 500;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
  overflow: hidden;
}
.tf-btn.btn-color-primary {
  background-color: var(--Primary);
}
.tf-btn.btn-color-primary:hover {
  background-color: var(--Secondary);
}
.tf-btn.btn-color-secondary {
  background-color: var(--Secondary);
}
.tf-btn.btn-color-secondary:hover {
  background-color: var(--Primary);
}
.tf-btn.btn-switch-text > span {
  display: inline-flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  height: 50px;
  padding: 0 38px;
}
.tf-btn.btn-switch-text .btn-double-text {
  transition: opacity 0.3s, transform 0.6s;
  transition-timing-function: cubic-bezier(0.15, 0.85, 0.31, 1);
  color: var(--White);
  line-height: 18px;
}
.tf-btn.btn-switch-text .btn-double-text:before {
  content: attr(data-text);
  display: inline-block;
  position: absolute;
  white-space: nowrap;
  top: 50%;
  opacity: 0;
  left: 50%;
  transform: translate(-50%, 100%);
  transition: opacity 0.5s, transform 0.8s;
  transition-timing-function: cubic-bezier(0.15, 0.85, 0.31, 1);
}
.tf-btn.btn-switch-text:hover .btn-double-text, .tf-btn.btn-switch-text:active .btn-double-text {
  transform: translateY(-200%);
}
.tf-btn.btn-switch-text:hover .btn-double-text:before, .tf-btn.btn-switch-text:active .btn-double-text:before {
  transform: translate(-50%, 150%);
  opacity: 1;
}

/*------------ range slider ---------------- */
/*range-slider
    ---------------------*/
.range-slider #range-two-val {
  margin-bottom: 10px;
  height: 1px;
  box-shadow: none;
  border: 0;
  background-color: var(--Text-top-bar);
  margin-bottom: 40px;
}
.range-slider #range-two-val .noUi-connects .noUi-connect {
  background-color: #2d2723;
}
.range-slider #range-two-val .noUi-origin .noUi-handle {
  cursor: pointer;
  width: 9px;
  height: 9px;
  border-radius: 50%;
  background-color: #2d2723;
  border: none;
  box-shadow: none;
  top: 0;
  right: -9px;
  transform: translate(-50%, -50%);
}
.range-slider #range-two-val .noUi-origin .noUi-handle:active {
  width: 17px !important;
  height: 17px !important;
  border: 4px solid #c3a27c !important;
  right: -17px !important;
}
.range-slider #range-two-val .noUi-origin .noUi-handle::after, .range-slider #range-two-val .noUi-origin .noUi-handle::before {
  display: none;
}
.range-slider .bottom {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 10px;
}
.range-slider .bottom a {
  color: var(--White);
  height: 40px;
  padding: 0 32px;
}
.range-slider .value {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.range-slider .value > div {
  font-size: 17px;
  font-weight: 400;
  line-height: 28px;
  color: var(--text);
}

/*------------ form ---------------- */
form {
  position: relative;
  z-index: 30;
}
form textarea,
form input[type=text],
form input[type=password],
form input[type=datetime],
form input[type=datetime-local],
form input[type=date],
form input[type=month],
form input[type=time],
form input[type=week],
form input[type=number],
form input[type=email],
form input[type=url],
form input[type=search],
form input[type=tel],
form input[type=color] {
  outline: 0;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  width: 100%;
  padding: 10px 17px;
  font-size: 14px;
  font-weight: 400;
  line-height: 29px;
  background-color: var(--Bg-2);
  border: 1px solid var(--Bg-2);
  color: var(--Text);
  overflow: hidden;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
form textarea::placeholder,
form input[type=text]::placeholder,
form input[type=password]::placeholder,
form input[type=datetime]::placeholder,
form input[type=datetime-local]::placeholder,
form input[type=date]::placeholder,
form input[type=month]::placeholder,
form input[type=time]::placeholder,
form input[type=week]::placeholder,
form input[type=number]::placeholder,
form input[type=email]::placeholder,
form input[type=url]::placeholder,
form input[type=search]::placeholder,
form input[type=tel]::placeholder,
form input[type=color]::placeholder {
  font-size: 15px;
  font-weight: 400;
  line-height: 29px;
  color: var(--Text);
}
form textarea:focus,
form input[type=text]:focus,
form input[type=password]:focus,
form input[type=datetime]:focus,
form input[type=datetime-local]:focus,
form input[type=date]:focus,
form input[type=month]:focus,
form input[type=time]:focus,
form input[type=week]:focus,
form input[type=number]:focus,
form input[type=email]:focus,
form input[type=url]:focus,
form input[type=search]:focus,
form input[type=tel]:focus,
form input[type=color]:focus {
  border: 1px solid var(--Primary);
  transition: all 0.3 ease-in-out;
  background-color: var(--White);
  box-shadow: 4px 4px 10px -5px rgba(0, 0, 0, 0.49);
  -webkit-box-shadow: 4px 4px 10px -5px rgba(0, 0, 0, 0.49);
  -moz-box-shadow: 4px 4px 10px -5px rgba(0, 0, 0, 0.49);
}
form textarea.style-default,
form input[type=text].style-default,
form input[type=password].style-default,
form input[type=datetime].style-default,
form input[type=datetime-local].style-default,
form input[type=date].style-default,
form input[type=month].style-default,
form input[type=time].style-default,
form input[type=week].style-default,
form input[type=number].style-default,
form input[type=email].style-default,
form input[type=url].style-default,
form input[type=search].style-default,
form input[type=tel].style-default,
form input[type=color].style-default {
  padding: 0;
  background-color: transparent;
}
form textarea.style-1,
form input[type=text].style-1,
form input[type=password].style-1,
form input[type=datetime].style-1,
form input[type=datetime-local].style-1,
form input[type=date].style-1,
form input[type=month].style-1,
form input[type=time].style-1,
form input[type=week].style-1,
form input[type=number].style-1,
form input[type=email].style-1,
form input[type=url].style-1,
form input[type=search].style-1,
form input[type=tel].style-1,
form input[type=color].style-1 {
  margin-bottom: 22px;
}
form .cols {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 23px 21px;
  width: 100%;
}
form .cols.cols-two > * {
  width: 49%;
}
form button,
form input[type=button],
form input[type=reset],
form input[type=submit] {
  background-color: transparent;
  overflow: hidden;
  padding: 0;
}
form textarea {
  height: 213px;
}

fieldset {
  margin-bottom: 0px;
  width: 100%;
}
fieldset .tf-input {
  -webkit-transition: all 0.1s ease;
  -moz-transition: all 0.1s ease;
  -ms-transition: all 0.1s ease;
  -o-transition: all 0.1s ease;
  transition: all 0.1s ease;
}

.form-send-email fieldset input,
.form-search fieldset input {
  padding-right: 90px;
  padding-left: 15px;
  font-size: 15px;
  line-height: 29px;
  color: var(--Secondary);
}
.form-send-email fieldset input::placeholder,
.form-search fieldset input::placeholder {
  font-size: 15px;
  line-height: 29px;
  font-weight: 400;
  color: var(--Black);
}
.form-send-email .button-submit,
.form-search .button-submit {
  position: absolute;
  right: 23px;
  top: 50%;
  -webkit-transform: translate(0, -50%);
  -moz-transform: translate(0, -50%);
  -ms-transform: translate(0, -50%);
  -o-transform: translate(0, -50%);
  transform: translate(0, -50%);
}
.form-send-email .button-submit button,
.form-search .button-submit button {
  cursor: pointer;
}

.form-search fieldset input {
  border-radius: 4px;
}
.form-search .button-submit i {
  color: var(--Primary);
  font-weight: 700;
}

/*------------ slider ---------------- */
.page-title-home .swiper-container i {
  color: var(--White);
  font-size: 78px;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.page-title-home .swiper-container i:hover {
  color: var(--Primary);
}
.page-title-home .swiper-container .swiper-button-prev {
  width: 80px;
  left: 119px;
  top: calc(54% + 5px);
}
.page-title-home .swiper-container .swiper-button-prev i {
  transform: rotate(180deg);
}
.page-title-home .swiper-container .swiper-button-prev::after {
  display: none;
}
.page-title-home .swiper-container .swiper-button-next {
  width: 80px;
  right: 119px;
  top: calc(54% + 5px);
}
.page-title-home .swiper-container .swiper-button-next:hover {
  color: var(--Primary);
}
.page-title-home .swiper-container .swiper-button-next::after {
  display: none;
}
.page-title-home .image {
  position: absolute;
  bottom: -35px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
  width: max-content;
  max-width: 100%;
}

.slide-effect-fade .swiper-slide .fade-item {
  transform: translateY(100px);
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.slide-effect-fade .swiper-slide.swiper-slide-active .fade-item {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}
.slide-effect-fade .swiper-slide.swiper-slide-active .fade-item.fade-item1 {
  transition-delay: 0.2s;
}
.slide-effect-fade .swiper-slide.swiper-slide-active .fade-item.fade-item2 {
  transition-delay: 0.3s;
}
.slide-effect-fade .swiper-slide.swiper-slide-active .fade-item.fade-item3 {
  transition-delay: 0.4s;
}
.slide-effect-fade .swiper-slide.swiper-slide-active .fade-item.fade-item4 {
  transition-delay: 0.5s;
}

.side-bar-item.filter > .content {
  padding: 39px 23px 34px 30px;
}

.slide-testimonials .button {
  color: var(--text);
  font-size: 70px;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.slide-testimonials .swiper-button-prev {
  left: 140px;
  top: calc(54% + 5px);
}
.slide-testimonials .swiper-button-prev i {
  transform: rotate(-180deg);
}
.slide-testimonials .swiper-button-prev:hover {
  color: var(--Primary);
}
.slide-testimonials .swiper-button-prev::after {
  display: none;
}
.slide-testimonials .swiper-button-next {
  right: 149px;
  top: calc(54% + 5px);
}
.slide-testimonials .swiper-button-next:hover {
  color: var(--Primary);
}
.slide-testimonials .swiper-button-next::after {
  display: none;
}

.section-our-product .button {
  color: var(--text);
  font-size: 70px;
}
.section-our-product .swiper-button-prev {
  left: -45px;
  top: calc(54% + 5px);
}
.section-our-product .swiper-button-prev i {
  transform: rotate(-180deg);
}
.section-our-product .swiper-button-prev::after {
  display: none;
}
.section-our-product .swiper-button-next {
  right: -45px;
  top: calc(54% + 5px);
}
.section-our-product .swiper-button-next::after {
  display: none;
}
.section-our-product.page-our-menu .button {
  height: 65px;
  width: 65px;
  background-color: var(--Secondary);
  color: var(--White);
  border-radius: 50%;
  font-size: 30px;
}
.section-our-product.page-our-menu .swiper-button-prev {
  left: -110px;
  top: calc(54% + 62px);
}
.section-our-product.page-our-menu .swiper-button-prev i {
  transform: unset;
}
.section-our-product.page-our-menu .swiper-button-next {
  right: -110px;
  top: calc(54% + 62px);
}

/*------------ blog ---------------- */
.article-blog-item {
  margin-bottom: 66px;
  position: relative;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.article-blog-item .image {
  height: 100%;
  width: 100%;
  margin-bottom: 11px;
  position: relative;
  z-index: 1;
  overflow: hidden;
}
.article-blog-item .image img {
  object-fit: cover;
  height: 389px;
  width: 100%;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.article-blog-item .content {
  position: relative;
  z-index: 2;
  overflow: hidden;
}
.article-blog-item .content .meta {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: start;
  margin-bottom: 4px;
  flex-wrap: wrap;
  color: #666666;
  gap: 0px 33px;
}
.article-blog-item .content .meta li {
  font-weight: 400;
  font-size: 13px;
  line-height: 35px;
}
.article-blog-item .content .meta li:not(:last-child) {
  position: relative;
}
.article-blog-item .content .meta li:not(:last-child)::after {
  position: absolute;
  width: 13px;
  height: 1px;
  content: "";
  top: 50%;
  right: -20px;
  background-color: var(--Primary);
}
.article-blog-item .content .title {
  margin-bottom: 22px;
}
.article-blog-item .content .title a {
  font-weight: 700;
  letter-spacing: 0.7px;
}
.article-blog-item .content .text {
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.article-blog-item .content .tf-btn-default {
  margin-top: -75px;
  padding-top: 22px;
  border-top: 1px solid var(--Line);
  letter-spacing: 1.3px;
  gap: 0px;
  position: relative;
  z-index: 4;
  background-color: var(--White);
}
.article-blog-item .content .tf-btn-default i {
  font-size: 23px;
  color: var(--Primary);
}
.article-blog-item:hover .image img {
  margin-top: -90px;
}
.article-blog-item:hover .text {
  opacity: 1;
  visibility: visible;
  margin-top: -9px;
}
.article-blog-item:hover .tf-btn-default {
  margin-top: 24px;
  padding-top: 22px;
}

.hover-img {
  overflow: hidden;
}
.hover-img img {
  -webkit-transition: all 1s cubic-bezier(0.3, 1, 0.35, 1) 0s;
  transition: all 1s cubic-bezier(0.3, 1, 0.35, 1) 0s;
  transition: transform 500ms ease;
}
.hover-img:hover img {
  -webkit-transform: scale3d(1.1, 1.1, 1.1);
  transform: scale3d(1.1, 1.1, 1.1);
  -webkit-transition: all 2s cubic-bezier(0.3, 1, 0.35, 2) 0s;
  transition: all 2s cubic-bezier(0.3, 1, 0.35, 2) 0s;
}

/*------------ testimonial ---------------- */
.testimonial-item .icon {
  margin: 0 auto 19px;
}
.testimonial-item .icon svg {
  max-width: 41px;
}
.testimonial-item .paragraph {
  font-size: 25px;
  line-height: 36px;
  font-weight: 700;
  letter-spacing: 0.46px;
  max-width: 1147.92px;
  margin: auto;
  margin-bottom: 51px;
  font-family: "CormorantInfant", sans-serif;
  color: var(--text);
}
.testimonial-item h5 {
  font-weight: 700;
  color: var(--text);
  margin-bottom: 9px;
  letter-spacing: 2px;
}
.testimonial-item .rating i {
  font-size: 19px;
}

/*------------ shop ---------------- */
.product-item .wrap-image {
  position: relative;
  margin-bottom: 30px;
  text-align: center;
  height: 204px;
  overflow: hidden;
}
.product-item .wrap-image img {
  object-fit: cover;
}
.product-item .wrap-image::before {
  content: "";
  position: absolute;
  display: block;
  width: 100%;
  height: 100%;
}
.product-item .wrap-image .wrap-icons {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  position: absolute;
  font-size: 17px;
  gap: 10px;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
}
.product-item .wrap-image .wrap-icons .icon {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 47px;
  width: 47px;
  background-color: var(--Secondary);
  border-radius: 50%;
  opacity: 0;
  visibility: hidden;
  cursor: pointer;
  transition: transform 0.4s ease 0.2s, opacity 0.4s ease 0s;
  margin-bottom: -34px;
  transform: translateY(20px);
}
.product-item .wrap-image .wrap-icons .add {
  transition-delay: 0.1s;
}
.product-item .wrap-image .wrap-icons .add svg {
  margin-left: -2px;
}
.product-item .wrap-image .wrap-icons .like {
  transition-delay: 0.2s;
}
.product-item .wrap-image .wrap-icons .like i {
  line-height: 20px;
  color: var(--White);
}
.product-item .content {
  text-align: center;
  gap: 24px;
}
.product-item .content .price {
  font-size: 20px;
  line-height: 24px;
  color: var(--Primary);
  font-weight: 600;
  margin-bottom: 12px;
}
.product-item .content .price span {
  color: #c8c8c8;
  text-decoration-line: line-through;
  margin-right: 20px;
  font-weight: 600;
}
.product-item .content .name {
  line-height: 26px;
  margin-bottom: 11px;
  font-weight: 700;
  letter-spacing: 1.5px;
}
.product-item .content .rating i {
  margin: 0 -3px;
}
.product-item.style-list {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
}
.product-item.style-list .wrap-image {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0;
  flex-shrink: 0;
  width: 200px;
}
.product-item.style-list .wrap-image img {
  object-fit: cover;
}
.product-item.style-list .content {
  flex-grow: 1;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  flex-wrap: wrap;
}
.product-item.style-list .content .price {
  margin-bottom: 0;
}
.product-item.style-list .content .name {
  margin-bottom: 0;
}
.product-item.style-list .content .rating {
  display: inline;
}
.product-item.style-list .content .rating i {
  margin: 0;
}
.product-item:hover .wrap-image::before {
  backdrop-filter: blur(2px);
  background: rgba(255, 255, 255, 0.01);
}
.product-item:hover .wrap-image .wrap-icons .icon {
  margin-bottom: 0;
  visibility: visible;
  opacity: 1;
  transform: translateY(0);
}

.rating {
  font-size: 18px;
  line-height: 25px;
  color: var(--Primary);
}
.rating i {
  font-size: 18px;
  margin: 0 -2px;
}

.shop-detail .tf-product-media-wrap .image-top {
  margin-bottom: 30px;
}
.shop-detail .tf-product-media-wrap .image-bottom {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  justify-content: space-between;
  gap: 15px;
}
.shop-detail .content-inner {
  padding-top: 34px;
  padding-left: 27px;
}
.shop-detail .content-inner .product-content {
  padding-bottom: 22px;
  margin-bottom: 23px;
  border-bottom: 1px solid var(--Text-top-bar);
}
.shop-detail .content-inner .product-content .product-title {
  font-weight: 700;
  color: var(--text);
  letter-spacing: 1.5px;
  margin-bottom: 15px;
  margin-left: -5px;
}
.shop-detail .content-inner .product-content .product-price {
  color: var(--text);
  font-size: 20px;
  font-weight: 500;
  line-height: 24px;
  margin-bottom: 20px;
  letter-spacing: 0.4px;
}
.shop-detail .content-inner .sort-description {
  padding-bottom: 23px;
  border-bottom: 1px solid var(--Text-top-bar);
  margin-bottom: 26px;
  letter-spacing: 0.2px;
}
.shop-detail .content-inner .title {
  font-family: "CormorantInfant", sans-serif;
  font-weight: 700;
  color: var(--text);
  margin-bottom: 21px;
}
.shop-detail .content-inner .wrap-quantity {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  gap: 15px;
  margin-bottom: 32px;
}
.shop-detail .content-inner .wrap-quantity .wg-quantity {
  position: relative;
}
.shop-detail .content-inner .wrap-quantity .wg-quantity input {
  width: 66px;
  height: 50px;
  background-color: var(--Secondary);
  border: none;
  box-shadow: none;
  color: var(--White);
  padding-left: 19px;
}
.shop-detail .content-inner .wrap-quantity .wg-quantity .btn-quantity {
  font-size: 15px;
  color: var(--White);
  cursor: pointer;
  position: absolute;
}
.shop-detail .content-inner .wrap-quantity .wg-quantity .minus-btn {
  bottom: 11px;
  right: 13px;
}
.shop-detail .content-inner .wrap-quantity .wg-quantity .plus-btn {
  top: 10px;
  right: 13px;
}
.shop-detail .content-inner .wrap-quantity .tf-btn {
  padding: 0;
}
.shop-detail .content-inner .wrap-quantity .tf-btn span {
  gap: 10px;
}
.shop-detail .content-inner .add-wishlist {
  padding-bottom: 24px;
  border-bottom: 1px solid var(--Text-top-bar);
  margin-bottom: 23px;
}
.shop-detail .content-inner .add-wishlist a {
  font-weight: 700;
  color: var(--text);
}
.shop-detail .content-inner .add-wishlist a i {
  font-size: 16px;
  margin-right: 8px;
  color: var(--Primary);
}
.shop-detail .content-inner .more-infor {
  padding-bottom: 23px;
  margin-bottom: 33px;
  border-bottom: 1px solid var(--Text-top-bar);
}
.shop-detail .content-inner .more-infor ul li {
  font-weight: 400;
  font-size: 15px;
  line-height: 26px;
  color: #080f19;
  margin-bottom: -1px;
}
.shop-detail .content-inner .more-infor ul li span {
  color: var(--body-text);
}
.shop-detail .content-inner .more-infor ul li a {
  color: var(--body-text);
}
.shop-detail .content-inner .more-infor ul li a:hover {
  color: var(--Primary);
}
.shop-detail .content-inner .share {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  gap: 27px;
}
.shop-detail .content-inner .share h5 {
  color: var(--text);
  font-weight: 700;
}
.shop-detail .content-inner .share .tf-social-icon li {
  border-color: var(--Text-top-bar);
  height: 35px;
  width: 35px;
}
.shop-detail .content-inner .share .tf-social-icon li i {
  font-size: 12px;
  color: var(--text);
}
.shop-detail .content-inner .share .tf-social-icon li:hover i {
  color: var(--White);
}

/*------------ map ---------------- */
.map-box {
  width: 409px;
  height: 409px;
  border-radius: 50%;
  overflow: hidden;
}

#map {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 100%;
}
#map .marker {
  background-image: url("../images/items/marker.png");
  background-size: cover;
  width: 35px;
  height: 50px;
  cursor: pointer;
}

.mapboxgl-ctrl-attrib a,
.mapboxgl-ctrl-attrib.mapboxgl-compact,
a.mapboxgl-ctrl-logo {
  display: none !important;
}

/*------------ widgets ---------------- */
.wg-pagination {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}
.wg-pagination li {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 49px;
  height: 49px;
  border-radius: 50%;
  border: 1px solid var(--Primary);
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  cursor: pointer;
}
.wg-pagination li a {
  font-size: 17px;
  color: var(--text-2);
}
.wg-pagination li:hover {
  background-color: var(--Primary);
}
.wg-pagination li:hover a {
  color: var(--White);
}
.wg-pagination li.active {
  background-color: var(--Primary);
}
.wg-pagination li.active a {
  color: var(--White);
}

.tf-social-icon {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: start;
  gap: 12px;
}
.tf-social-icon li {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 45px;
  width: 45px;
  font-size: 16px;
  border-radius: 50%;
  border: 1px solid var(--Bg-1);
  cursor: pointer;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.tf-social-icon li i {
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  color: var(--White);
}
.tf-social-icon li:hover {
  background-color: var(--Primary);
}
.tf-social-icon.style-2 {
  gap: 10px;
}
.tf-social-icon.style-2 li {
  border: none;
  background-color: #e5e5e4;
  width: 36.67px;
  height: 37px;
}
.tf-social-icon.style-2 li a i {
  color: var(--text);
  width: 13px;
  height: 12px;
}
.tf-social-icon.style-2 li:hover {
  background-color: var(--Primary);
}
.tf-social-icon.style-2 li:hover a i {
  color: var(--White);
}

.checkbox-item label {
  padding-left: 28px;
  cursor: pointer;
  position: relative;
  letter-spacing: 0.4px;
}
.checkbox-item label input {
  position: absolute;
  opacity: 0;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
}
.checkbox-item label input:checked ~ .btn-checkbox {
  background-color: var(--White);
}
.checkbox-item label input:checked ~ .btn-checkbox:after {
  display: block;
}
.checkbox-item label input:checked ~ .btn-radio:after {
  display: block;
}
.checkbox-item label .btn-checkbox {
  position: absolute;
  left: 2px;
  top: 50%;
  transform: translateY(-50%);
  width: 13px;
  height: 12px;
  background-color: transparent;
  border: 1px solid var(--Border);
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.checkbox-item label .btn-checkbox:after {
  content: "\e93a";
  font-family: "icomoon" !important;
  position: absolute;
  font-size: 12px;
  font-weight: 900;
  top: -2px;
  left: 0px;
  color: var(--Black);
  display: none;
}
.checkbox-item label .btn-radio {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  background-color: transparent;
  border-radius: 50%;
  border: 1px solid var(--Border);
}
.checkbox-item label .btn-radio::after {
  position: absolute;
  display: none;
  content: "";
  height: 16px;
  width: 16px;
  top: 0;
  left: 0;
  border-radius: 50%;
  border: 3px solid black;
  background-color: var(--White);
}

.widget-video {
  position: relative;
  overflow: hidden;
  margin-bottom: 45px;
  cursor: pointer;
}
.widget-video .popup-youtube {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: center;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  position: absolute;
  height: 72px;
  width: 72px;
  border-radius: 50%;
  background-color: var(--White);
  color: var(--Black);
  visibility: hidden;
  opacity: 0;
}
.widget-video .popup-youtube i {
  transform: translateX(2px);
  font-size: 28.67px;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.widget-video .popup-youtube .wave {
  position: absolute;
  z-index: -1;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 109.04px;
  height: 109.04px;
  background-color: rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  animation: ripple 1s infinite;
}
.widget-video .popup-youtube .wave-1 {
  z-index: -1;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 132px;
  height: 132px;
  background-color: rgb(255, 255, 255);
  border-radius: 50%;
  animation: ripple-1 1s infinite;
}
.widget-video:hover .popup-youtube {
  visibility: visible;
  opacity: 1;
}
.widget-video:hover .popup-youtube i {
  color: var(--Primary);
}
@keyframes ripple {
  0% {
    width: 86.09px;
    height: 86.09px;
    opacity: 1;
  }
  100% {
    width: 109.04px;
    height: 109.04px;
    opacity: 0;
  }
}
@keyframes ripple-1 {
  0% {
    width: 86.09px;
    height: 86.09px;
    opacity: 1;
  }
  100% {
    width: 132px;
    height: 132px;
    opacity: 0;
  }
}

.counter .number-counter {
  text-align: center;
  position: relative;
}
.counter .number-counter .counter-content {
  font-size: 70px;
  line-height: 45px;
  font-family: "CormorantInfant", sans-serif;
  font-weight: 700;
  width: 132px;
  color: var(--text);
  margin: 0 auto 27px;
}
.counter .number-counter p {
  color: var(--text);
  letter-spacing: 0.8px;
}
.counter .number-counter::after {
  position: absolute;
  content: "";
  top: 50%;
  right: -23px;
  width: 1px;
  height: 136px;
  border-right: 1px dashed #635f5c;
  transform: translateY(-50%);
}
.counter .number-counter.last-child::after {
  display: none;
}

.list-check {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  gap: 22px;
  flex-direction: column;
}
.list-check li {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  gap: 12px;
  letter-spacing: 0.5px;
  font-size: 17px;
  font-weight: 500;
  color: var(--text-2);
  font-family: "CormorantInfant", sans-serif;
}
.list-check li i {
  flex: none;
  height: 16px;
  width: 16px;
  background-color: var(--Primary);
  border-radius: 50%;
  font-size: 12px;
  line-height: 16px;
  text-align: center;
  color: var(--White);
}

.follow-item {
  position: relative;
  overflow: hidden;
  cursor: pointer;
}
.follow-item .wrap-image {
  min-width: 218px;
}
.follow-item .wrap-image img {
  width: 100%;
  object-fit: cover;
}
.follow-item::before {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  z-index: 1;
  top: 100%;
  left: 0;
  background-color: rgba(18, 15, 12, 0.5);
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.follow-item .icon {
  position: absolute;
  top: 40%;
  left: 50%;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  transform: translate(-50%, -50%);
  z-index: 2;
}
.follow-item .icon a {
  font-size: 20px;
  color: var(--White);
}
.follow-item:hover::before {
  top: 0;
  opacity: 1;
  visibility: visible;
}
.follow-item:hover .icon {
  opacity: 1;
  visibility: visible;
  top: 50%;
  transition-delay: 0.3s;
}

.icon-box {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}
.icon-box.last-child::after {
  display: none;
}
.icon-box::after {
  position: absolute;
  content: "";
  top: 50%;
  right: -15px;
  width: 1px;
  height: 193px;
  border-right: 1px dashed #635f5c;
  transform: translateY(-50%);
}
.icon-box .icon {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 66px;
  height: 117px;
  width: 117px;
  color: var(--White);
  border-radius: 50%;
  background-color: var(--sort-text);
  margin-bottom: 21px;
  -webkit-transition: all 0.5s cubic-bezier(0.61, 1, 0.88, 1);
  transition: all 0.5s cubic-bezier(0.61, 1, 0.88, 1);
}
.icon-box span {
  font-family: "CormorantInfant", sans-serif;
  font-weight: 700;
  letter-spacing: 1px;
}
.icon-box:hover .icon {
  background-color: var(--Secondary);
}

.wg-menu-item {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  gap: 20px;
  -webkit-transition: all 0.5s cubic-bezier(0.61, 1, 0.88, 1);
  transition: all 0.5s cubic-bezier(0.61, 1, 0.88, 1);
}
.wg-menu-item .wrap-image {
  width: 75px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}
.wg-menu-item .wrap-image img {
  width: 100%;
  height: 75px;
  object-fit: cover;
}
.wg-menu-item .content {
  width: 100%;
  margin-right: 15px;
}
.wg-menu-item .content .top {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}
.wg-menu-item .content .top h4,
.wg-menu-item .content .top .price {
  font-weight: 700;
  color: var(--text);
}
.wg-menu-item .content .top h4 {
  letter-spacing: 0.5px;
}
.wg-menu-item .content .top .dot {
  flex-grow: 1;
  height: 12px;
  margin-left: 10px;
  margin-right: 10px;
  border-bottom: 1px dashed #d2d2d3;
}
.wg-menu-item .content p {
  letter-spacing: 0.2px;
}
.wg-menu-item:hover {
  -webkit-transform: translateX(15px);
  transform: translateX(15px);
}

.tf-shop-control {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20px;
}
.tf-shop-control p {
  font-size: 17px;
  line-height: 30px;
  color: var(--text);
}
.tf-shop-control .option {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  gap: 23px;
}
.tf-shop-control .option .btn-list {
  font-size: 35px;
  color: var(--text);
  cursor: pointer;
}
.tf-shop-control .option .btn-grid {
  cursor: pointer;
}
.tf-shop-control .tf-control-sorting .tf-dropdown-sort {
  padding: 17px 15px;
  border: 1px solid var(--Black);
  cursor: pointer;
}
.tf-shop-control .tf-control-sorting .tf-dropdown-sort .btn-select {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  gap: 30px;
  font-size: 15px;
  color: var(--text);
  justify-content: space-between;
}
.tf-shop-control .tf-control-sorting .tf-dropdown-sort .dropdown-menu {
  min-width: 180px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.1019607843);
  margin-top: 10px !important;
  border: 0;
  padding-top: 15px;
  padding-bottom: 15px;
}
.tf-shop-control .tf-control-sorting .tf-dropdown-sort .select-item {
  cursor: pointer;
  font-size: 15px;
  font-weight: 400;
  color: var(--text);
  padding: 0 20px;
  line-height: 30px;
  width: 100%;
  border-left: 2px solid var(--White);
  margin-right: 5px;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.tf-shop-control .tf-control-sorting .tf-dropdown-sort .select-item::before {
  height: 100%;
  content: "";
  width: 6px;
  position: absolute;
  z-index: 2;
  left: 2px;
  top: 0;
  background-color: var(--White);
}
.tf-shop-control .tf-control-sorting .tf-dropdown-sort .select-item.active, .tf-shop-control .tf-control-sorting .tf-dropdown-sort .select-item:hover {
  background-color: var(--Text-top-bar);
  border-left: 2px solid var(--Black);
}

.service-item {
  position: relative;
  overflow: hidden;
}
.service-item .wrap-image {
  position: relative;
}
.service-item .wrap-image img {
  width: 100%;
}
.service-item .wrap-image::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(0deg, rgba(18, 15, 12, 0.651), rgba(18, 15, 12, 0));
}
.service-item .content {
  position: absolute;
  bottom: -130px;
  left: 0;
  width: 100%;
  text-align: center;
  padding-bottom: 89px;
  z-index: 5;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.service-item .content .icon,
.service-item .content h2,
.service-item .content p,
.service-item .content a {
  color: var(--White);
}
.service-item .content .icon {
  font-size: 77px;
  margin-bottom: 15px;
}
.service-item .content h2 {
  font-weight: 700;
  margin-bottom: 18px;
  letter-spacing: 1.7px;
}
.service-item .content p {
  margin-bottom: 24px;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  letter-spacing: 0.5px;
}
.service-item .content .tf-btn-default {
  visibility: hidden;
  opacity: 0;
  justify-content: center;
  position: relative;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  letter-spacing: 0.4px;
}
.service-item .content .tf-btn-default i {
  font-size: 18px;
}
.service-item .content .tf-btn-default::after {
  position: absolute;
  content: "";
  height: 1px;
  background-color: var(--White);
  width: 100px;
  bottom: 0;
  left: calc(50% - 2px);
  transform: translateX(-50%);
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.service-item .content .tf-btn-default:hover {
  color: var(--Primary);
}
.service-item .content .tf-btn-default:hover::after {
  background-color: var(--Primary);
}
.service-item:hover .content {
  bottom: 0;
}
.service-item:hover .content p {
  visibility: visible;
  opacity: 1;
}
.service-item:hover .content .tf-btn-default {
  visibility: visible;
  opacity: 1;
}

.wg-404 {
  background-image: url(../images/background/bg-404.jpg);
  height: 100vh;
}
.wg-404 .content {
  padding: 190px 0;
  text-align: center;
}
.wg-404 .content .title {
  margin-bottom: -4px;
  position: relative;
}
.wg-404 .content .title::before {
  position: absolute;
  content: "4";
  font-family: "CormorantInfant", sans-serif;
  font-weight: 700;
  top: 47px;
  left: 50%;
  transform: translateX(-330px);
  font-size: 262.24px;
  color: var(--text);
}
.wg-404 .content .title::after {
  position: absolute;
  content: "4";
  font-family: "CormorantInfant", sans-serif;
  top: 47px;
  right: 50%;
  font-weight: 700;
  color: var(--text);
  transform: translateX(338px);
  font-size: 262.24px;
}
.wg-404 .content .sub-title {
  font-size: 40px;
  font-weight: 700;
  font-family: "CormorantInfant", sans-serif;
  color: var(--text);
  margin-bottom: 17px;
  letter-spacing: 2px;
}
.wg-404 .content p {
  font-size: 17px;
  color: var(--text);
  line-height: 28px;
  margin-bottom: 41px;
  letter-spacing: 0.1px;
}
.wg-404 .content .tf-btn {
  margin: 0 auto;
}
.wg-404 .content .tf-btn:hover {
  color: var(--White);
}

.our-menu-item {
  position: relative;
}
.our-menu-item .wrap-image {
  overflow: hidden;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.our-menu-item .wrap-image img {
  object-fit: cover;
}
.our-menu-item .content {
  position: absolute;
  padding: 45px 20px 0;
  border-radius: 150px 150px 0 0;
  background-color: var(--Secondary);
  top: 40px;
  left: 38px;
  right: 37px;
  bottom: 38px;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  transform: scale(0.8);
}
.our-menu-item .content .wrap {
  width: 100%;
  height: 100%;
  text-align: center;
  padding: 59px 19px 0;
}
.our-menu-item .content .price {
  padding: 6px 24px;
  background-color: var(--Primary);
  display: inline-block;
  font-size: 17px;
  line-height: 23px;
  border-radius: 17.5px;
  font-weight: 600;
  color: var(--White);
  margin-bottom: 22px;
}
.our-menu-item .content .title {
  color: var(--White);
  font-family: "CormorantInfant", sans-serif;
  line-height: 31px;
  font-size: 25px;
  font-weight: 700;
  letter-spacing: 1.2px;
  margin-bottom: 21px;
}
.our-menu-item .content .title a {
  color: var(--White);
}
.our-menu-item .content .title:hover a {
  color: var(--Primary);
}
.our-menu-item .content p {
  color: var(--White);
}
.our-menu-item:hover .wrap-image {
  border-radius: 200px 200px 0 0;
}
.our-menu-item:hover .content {
  transform: scale(1);
  opacity: 1;
  visibility: visible;
}

.portfolio-item .wrap-image {
  overflow: hidden;
  margin-bottom: 28px;
}
.portfolio-item .wrap-image img {
  width: 100%;
  height: 432px;
  object-fit: cover;
}
.portfolio-item .content {
  text-align: center;
}
.portfolio-item .content .title {
  font-family: "CormorantInfant", sans-serif;
  color: var(--text);
  font-weight: 700;
  letter-spacing: 1px;
  margin-bottom: 5px;
}
.portfolio-item .content p {
  color: var(--Primary);
  font-weight: 500;
  letter-spacing: 0.4px;
}

.box-team .wrap-image {
  width: 100%;
  overflow: hidden;
}
.box-team .wrap-image img {
  width: 100%;
}
.box-team .content {
  position: relative;
  padding: 73px 30px 34px;
  text-align: center;
  background-color: #2d2723;
}
.box-team .content .tag-author {
  text-align: center;
  padding: 23px 0 17px;
  border-radius: 50%;
  position: absolute;
  background-color: var(--White);
  top: -96px;
  transform: translateY(50%);
  right: 30px;
  left: 30px;
}
.box-team .content .tag-author .name {
  font-weight: 700;
  color: var(--text-2);
  letter-spacing: 0.5px;
  line-height: 29px;
}
.box-team .content .desc {
  color: var(--Text-top-bar);
  margin-bottom: 28px;
}
.box-team .content .list-sosial {
  background-color: var(--Primary);
  padding: 0 25px;
  width: max-content;
  border-radius: 35px;
  height: 35px;
  margin: auto;
  gap: 19px;
}
.box-team .content .list-sosial li {
  opacity: unset;
  cursor: pointer;
}
.box-team .content .list-sosial li i {
  color: var(--White);
  transition: all 0.3s ease-in;
}
.box-team .content .list-sosial li:hover i {
  color: var(--Secondary);
}

.cart-item {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  position: relative;
  gap: 22px;
  margin-bottom: 27px;
}
.cart-item .wrap-image {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 76px;
  height: 82px;
  overflow: hidden;
  background-color: var(--White);
}
.cart-item .wrap-image img {
  object-fit: cover;
}
.cart-item .content {
  flex-grow: 1;
  padding-top: 16px;
}
.cart-item .content .price {
  margin-bottom: 2px;
  color: var(--text);
  font-size: 17px;
  line-height: 24px;
}
.cart-item .content .name {
  font-weight: 700;
  color: var(--text);
  letter-spacing: 0.1px;
}
.cart-item .content .close-button {
  position: absolute;
  top: 0;
  right: -3px;
  font-weight: 600;
  font-size: 11px;
  color: var(--text);
  cursor: pointer;
}
.cart-item:not(:last-child) {
  padding-bottom: 25px;
  border-bottom: 1px solid #e5dccf;
}

.progress-wrap {
  position: fixed;
  bottom: 40px;
  right: 40px;
  height: 40px;
  width: 40px;
  cursor: pointer;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border-radius: 999px;
  z-index: 98;
  opacity: 0;
  visibility: hidden;
  -webkit-transform: translateY(20px);
  -ms-transform: translateY(20px);
  transform: translateY(20px);
  -webkit-transition: all 400ms linear;
  -o-transition: all 400ms linear;
  transition: all 400ms linear;
  background: var(--White);
  border: 1.2px solid rgba(134, 134, 134, 0.12);
}
.progress-wrap::after {
  position: absolute;
  content: "\e916";
  font-family: "icomoon";
  text-align: center;
  line-height: 40px;
  font-size: 20px;
  color: var(--Black);
  height: 40px;
  width: 40px;
  cursor: pointer;
  z-index: 1;
  -webkit-transition: all 400ms linear;
  -o-transition: all 400ms linear;
  transition: all 400ms linear;
}
.progress-wrap.active-progress {
  opacity: 1;
  visibility: visible;
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  transform: translateY(0);
}
.progress-wrap svg path {
  fill: none;
}
.progress-wrap svg.progress-circle path {
  box-sizing: border-box;
  stroke: var(--Black);
  stroke-width: 4;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  transition-duration: 0.4s;
  transition-timing-function: linear;
}

.thumbs-slider .tf-product-media-main {
  overflow: hidden;
  margin-bottom: 29px;
}
.thumbs-slider .tf-product-media-thumbs .item {
  cursor: pointer;
  height: 100%;
}
.thumbs-slider .swiper-wrapper {
  flex-direction: row;
}
.thumbs-slider .swiper-wrapper .swiper-slide {
  max-height: max-content !important;
}

/*------------ sections ---------------- */
.tf-spacing-1 {
  padding-top: 0;
  padding-bottom: 130px;
}

.tf-spacing-2 {
  padding-top: 133px;
  padding-bottom: 135px;
}

.tf-spacing-3 {
  padding-top: 127px;
  padding-bottom: 136px;
}

.tf-spacing-4 {
  padding-top: 75px;
  padding-bottom: 100px;
}

.tf-spacing-5 {
  padding-top: 0;
  padding-bottom: 122px;
}

.tf-spacing-6 {
  padding-top: 107px;
  padding-bottom: 131px;
}

.tf-spacing-7 {
  padding-top: 134px;
  padding-bottom: 136px;
}

.tf-spacing-8 {
  padding-top: 126px;
  padding-bottom: 136px;
}

.tf-spacing-9 {
  padding-top: 0;
  padding-bottom: 70px;
}

.tf-spacing-10 {
  padding-top: 0;
  padding-bottom: 126px;
}

.tf-spacing-11 {
  padding-top: 132px;
  padding-bottom: 134px;
}

.tf-spacing-12 {
  padding-bottom: 84px;
}

.tf-spacing-13 {
  padding-top: 0;
  padding-bottom: 95px;
}

.tf-spacing-14 {
  padding-top: 0;
  padding-bottom: 137px;
}

.tf-spacing-15 {
  padding-top: 0;
  padding-bottom: 223px;
}

.tf-spacing-16 {
  padding-top: 134px;
  padding-bottom: 116px;
}

.tf-spacing-17 {
  padding-top: 134px;
  padding-bottom: 130px;
}

.tf-spacing-18 {
  padding-top: 134px;
  padding-bottom: 131px;
}

.main-content {
  padding-top: 135px;
  padding-bottom: 135px;
}
.main-content.home-page {
  padding-bottom: 128px;
}
.main-content.page-portfolio {
  padding-top: 127px;
}
.main-content.page-contact-us {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}
.main-content.page-shop-detail {
  padding-bottom: 127px;
}
.main-content.page-our-menu {
  padding-top: 0 !important;
  padding-bottom: 0;
}
.main-content.page-our-team .item1 {
  position: absolute;
  top: -31px;
  left: 0;
}
.main-content.page-about {
  padding-top: 135px;
  padding-bottom: 71px;
}
.main-content.page-shop {
  padding-top: 116px;
}
.main-content.page-history {
  padding-top: 130px;
  padding-bottom: 136px;
}
.main-content.page-history .item {
  position: absolute;
  right: 0;
  bottom: 32%;
}

.grid-layout-3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 68px 30px;
}

.grid-layout-5 {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 20px 20px;
}

/*page-title
--------------------------------------------------------------------*/
.page-title {
  background-image: url(../images/background/background1.jpg);
  padding-top: 202px;
  padding-bottom: 45px;
  background-attachment: fixed;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
}
.page-title .fade-item {
  transform: translateY(100px);
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.page-title::before {
  content: "";
  width: 100%;
  height: 100%;
  background-color: #000000;
  opacity: 50%;
  left: 0px;
  top: 0px;
  position: absolute;
}
.page-title .content {
  text-align: center;
  position: relative;
  z-index: 1;
}
.page-title .content .icon-top {
  max-width: 143px;
  margin: 0 auto 25px;
}
.page-title .content .title {
  font-weight: 700;
  color: var(--White);
  margin-bottom: 17px;
  letter-spacing: 5px;
}
.page-title .content .under-line {
  max-width: 270px;
  margin: 0 auto 98px;
}
.page-title .content .breadcrumbs {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}
.page-title .content .breadcrumbs li {
  color: var(--White);
  font-size: 13px;
  line-height: 27px;
  letter-spacing: 1px;
}
.page-title .content .breadcrumbs li a {
  color: var(--White);
}
.page-title .content .breadcrumbs li:hover a {
  color: var(--Primary);
}
.page-title .content .breadcrumbs li:last-child {
  text-decoration: underline;
}
/* .page-title.style-2 {
  background-image: url(../images/background/background1.jpg);
  padding-bottom: 222px;
  padding-top: 270px;
} */
.page-title.style-2 .content .icon-top {
  margin-bottom: 28px;
}
.page-title.style-2 .content .sub-title {
  font-style: italic;
  font-weight: 700;
  color: var(--White);
  font-family: "CormorantInfant", sans-serif;
  letter-spacing: 0.5px;
  margin-bottom: 2px;
}
.page-title.style-2 .content .under-line {
  margin-bottom: 16px;
}
.page-title.style-2 .content .title {
  margin-bottom: 38px;
}
.page-title.style-2 .content .tf-btn {
  background-color: var(--White);
  margin-left: auto;
  margin-right: auto;
  color: var(--text);
}
.page-title.style-2 .content .tf-btn .btn-double-text {
  color: var(--text);
}
.page-title.page-shop {
  background-image: url(../images/background/background1.jpg);
  background-repeat: no-repeat;
}
.page-title.page-portfolio {
  background-image: url(../images/background/background1.jpg);
  background-repeat: no-repeat;
}
.page-title.page-contact-us {
  background-image: url(../images/background/background1.jpg);
  background-repeat: no-repeat;
}
.page-title.page-about {
  background-image: url(../images/background/background1.jpg);
  background-repeat: no-repeat;
}

.heading-section {
  position: relative;
  z-index: 5;
}
.heading-section .sub-title {
  font-weight: 500;
  color: var(--Primary);
  margin-bottom: 13px;
  letter-spacing: 1.3px;
}
.heading-section .title {
  color: var(--text);
  font-family: "CormorantInfant", sans-serif;
  font-weight: 700;
  margin-bottom: 11px;
  letter-spacing: 2.2px;
}
.heading-section .short-desc {
  font-weight: 700;
  font-style: italic;
  font-size: 25px;
  line-height: 33px;
  font-family: "CormorantInfant", sans-serif;
  color: var(--text);
  letter-spacing: 0.2px;
  margin-bottom: 0 !important;
}

/*blog-page
----------------------------------------------------------------*/
/* .section-sign-up .wrap-sign-up {
  background-image: url(../images/background/bg-blog-2.jpg);
  padding-top: 72px;
  padding-bottom: 99px;
  text-align: center;
  position: relative;
} */
.section-sign-up .wrap-sign-up .title {
  font-weight: 700;
  margin-bottom: 12px;
  letter-spacing: 0.4px;
}
.section-sign-up .wrap-sign-up .text-paragraph {
  text-align: center;
  margin-bottom: 28px;
  max-width: 724px;
  margin-left: auto;
  margin-right: auto;
}
.section-sign-up .wrap-sign-up .form-send-email {
  max-width: 542px;
  margin: 0 auto;
}
.section-sign-up .wrap-sign-up .form-send-email fieldset {
  width: 100%;
}
.section-sign-up .wrap-sign-up .form-send-email .button-submit i {
  font-size: 20px;
  color: var(--Primary);
}
.section-sign-up .wrap-sign-up .image {
  position: absolute;
  bottom: -32px;
  left: 50%;
  transform: translateX(-50%);
  width: max-content;
  max-width: 100%;
}

/*blog-single
---------------------------------------------------------------*/
.section-blog-single .meta {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 26px;
  gap: 15px 34px;
  flex-wrap: wrap;
}
.section-blog-single .meta li {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  gap: 13px;
}
.section-blog-single .meta li .icon {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 1px solid rgba(17, 17, 17, 0.2);
}
.section-blog-single .meta li .icon i {
  font-size: 11px;
  color: var(--Black);
}
.section-blog-single .meta li p {
  color: rgb(17, 17, 17);
}
.section-blog-single .title-main {
  margin-bottom: 39px;
  text-align: center;
  font-weight: 700;
  letter-spacing: 1.7px;
  color: var(--text-2);
}
.section-blog-single .main-post {
  margin-bottom: 42px;
}
.section-blog-single .paragraph {
  margin-bottom: 33px;
  letter-spacing: 0.1px;
}
.section-blog-single .paragraph.style-2 {
  margin-bottom: 15px;
}
.section-blog-single .paragraph.style-3 {
  margin-bottom: 38px;
}
.section-blog-single h4 {
  margin-bottom: 13px;
  letter-spacing: 0.4px;
}
.section-blog-single h4.style-2 {
  margin-bottom: 16px;
}
/* .section-blog-single .wg-block-quote {
  background-image: url(../images/background/bg-blog-2.jpg);
  border-radius: 10px;
  padding: 41px 62px 43px;
  position: relative;
  margin-bottom: 27px;
  margin-top: 34px;
} */
.section-blog-single .wg-block-quote p {
  font-style: italic;
  letter-spacing: 0.3px;
  color: var(--text);
  line-height: 28px;
}
.section-blog-single .wg-block-quote h6 {
  font-style: italic;
  margin-top: 19px;
  letter-spacing: 1.3px;
  color: var(--text);
  position: relative;
}
.section-blog-single .wg-block-quote h6::after {
  position: absolute;
  content: "";
  background-image: url(../icons/blockquote.svg);
  height: 33px;
  width: 31px;
  right: 10px;
  top: -9px;
}
.section-blog-single .bot {
  padding: 17px 0 18px;
  border-top: 1px solid #dedede;
  border-bottom: 1px solid #dedede;
  margin-bottom: 70px;
  gap: 20px;
}
.section-blog-single .bot .tags {
  font-size: 15px;
  color: #171412;
  font-weight: 500;
}
.section-blog-single .bot .tags i {
  font-size: 14px;
  color: var(--Primary);
}
.section-blog-single .bot .tags a {
  font-size: 15px;
  letter-spacing: 0.3px;
  color: #171412;
  font-weight: 500;
}
.section-blog-single .bot .wg-social {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: start;
  gap: 23px;
}
.section-blog-single .bot .wg-social span {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: start;
  gap: 7px;
  color: var(--text);
}
.section-blog-single .bot .wg-social span i {
  color: var(--Primary);
}
.section-blog-single .comment {
  padding-bottom: 38px;
  border-bottom: 1px solid var(--Text-top-bar);
  margin-bottom: 71px;
}
.section-blog-single .comment .title {
  margin-bottom: 37px;
  color: var(--text);
  letter-spacing: 0.3px;
}
.section-blog-single .comment .wg-commnent .wrap-author {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 18px;
}
.section-blog-single .comment .wg-commnent .wrap-author .author {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: start;
  gap: 21px;
}
.section-blog-single .comment .wg-commnent .wrap-author .author .image {
  width: 68px;
  height: 68px;
  border-radius: 50%;
  border: 2px solid #c3a27c;
  overflow: hidden;
}
.section-blog-single .comment .wg-commnent .wrap-author .author .image img {
  height: 100%;
  object-fit: cover;
}
.section-blog-single .comment .wg-commnent .wrap-author .author .content .name {
  letter-spacing: 0.8px;
}
.section-blog-single .comment .wg-commnent .wrap-author .author .content .name a {
  color: var(--text);
}
.section-blog-single .comment .wg-commnent .wrap-author .author .content .name:hover a {
  color: var(--Primary);
}
.section-blog-single .comment .wg-commnent .wrap-author .author .content .time {
  letter-spacing: -0.8px;
}
.section-blog-single .comment .wg-commnent .wrap-author .author .content .decs {
  letter-spacing: 0.1px;
}
.section-blog-single .comment .wg-commnent .wrap-author .tf-btn {
  text-transform: none;
  gap: 3px;
  padding: 10px 15px 10px 11px;
  color: var(--White);
}

.box-send .title {
  margin-bottom: 34px;
  color: var(--text);
  letter-spacing: 0.3px;
}
.box-send p {
  margin-bottom: 26px;
  letter-spacing: 0.2px;
}
.box-send .form-add-review .cols {
  margin-bottom: 24px;
}
.box-send .form-add-review .tf-btn {
  margin: 50px auto 0;
}

/*history
---------------------------------------------------------------*/
.history-main {
  position: relative;
  padding-bottom: 149px;
}
.history-main::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
  width: 1px;
  height: 100%;
  background-color: var(--Primary);
  z-index: 1;
}
.history-main .history-box:last-child {
  margin-bottom: 0 !important;
}
.history-main .history-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  margin-bottom: 101px;
}
.history-main .history-box .wrap-image {
  position: relative;
  max-width: 517px;
}
.history-main .history-box .wrap-image .item-history {
  z-index: -1;
  position: absolute;
  top: -149px;
  left: -195px;
}
.history-main .history-box::after {
  position: absolute;
  content: "";
  height: 9px;
  width: 9px;
  border-radius: 50%;
  background-color: var(--Primary);
  left: 50%;
  transform: translate(-50%, -50%);
  top: 50%;
  z-index: 1;
}
.history-main .history-box::before {
  position: absolute;
  content: "";
  height: 1px;
  width: 46px;
  background-color: var(--Primary);
  left: 50%;
  top: 50%;
  z-index: 1;
}
.history-main .history-box .content {
  width: 429px;
  padding-top: 6px;
}
.history-main .history-box .content .time {
  font-style: italic;
  font-weight: 700;
  color: var(--Primary);
  margin-bottom: 17px;
}
.history-main .history-box .content .title {
  font-weight: 700;
  color: var(--text);
  letter-spacing: 1.4px;
  margin-bottom: 17px;
  line-height: 35px;
}
.history-main .history-box .content p {
  font-weight: 500;
}
.history-main .history-box.style-2 {
  margin-bottom: 100px;
}
.history-main .history-box.style-2::before {
  left: unset;
  right: 50%;
}
.history-main .history-box.style-2 .wrap-image {
  position: relative;
}
.history-main .history-box.style-2 .wrap-image::after {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  max-width: 503px;
  max-height: 263px;
  background-color: #f4ecdf;
  left: 21px;
  bottom: -26px;
}
.history-main .history-box.style-2 .wrap-image img {
  position: relative;
  z-index: 1;
}
.history-main .history-box.style-2 .wrap-image .item-history2 {
  position: absolute;
  bottom: -54px;
  right: -78px;
}
.history-main .history-box.style-2 .content {
  margin-right: 0;
}
.history-main .history-box.style-2 .content .title {
  margin-bottom: 12px;
}
.history-main .history-box.style-3 {
  margin-bottom: 98px;
  align-items: unset;
}
.history-main .history-box.style-3 .wrap-image img {
  margin-bottom: 21px;
}
.history-main .history-box.style-3 .content {
  padding-top: 143px;
}
.history-main .history-box.style-3 .content p {
  margin-bottom: 12px;
}
.history-main .history-box.style-4 {
  align-items: unset;
}
.history-main .history-box.style-4::before {
  left: unset;
  right: 50%;
}
.history-main .history-box.style-4 .wrap-image .item-history3 {
  position: absolute;
  bottom: -78px;
  left: -47px;
}
.history-main .history-box.style-4 .content {
  padding-top: 87px;
  margin-right: 0;
  margin-left: 100px;
}
.history-main .history-box.style-4 .content .time {
  margin-bottom: 16px;
}
.history-main .history-box.style-4 .content .title {
  margin-bottom: 12px;
}

/*section-shop
---------------------------------------------------------------*/
.section-shop .tf-shop-control {
  margin-bottom: 78px;
}
.section-shop .wg-shop-content {
  margin-bottom: 4px;
}
.section-shop .wg-shop-content .product-item {
  margin-bottom: 64px;
}
.section-shop .cart-item {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  position: relative;
  gap: 22px;
  margin-bottom: 27px;
}
.section-shop .cart-item .wrap-image {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 76px;
  height: 82px;
  overflow: hidden;
  background-color: var(--White);
}
.section-shop .cart-item .wrap-image img {
  object-fit: cover;
}
.section-shop .cart-item .content {
  flex-grow: 1;
  padding-top: 16px;
}
.section-shop .cart-item .content .price {
  margin-bottom: 2px;
  color: var(--text);
  font-size: 17px;
  line-height: 24px;
}
.section-shop .cart-item .content .name {
  font-weight: 700;
  color: var(--text);
  letter-spacing: 0.1px;
}
.section-shop .cart-item .content .close-button {
  position: absolute;
  top: 0;
  right: -3px;
  font-weight: 600;
  font-size: 11px;
  color: var(--text);
  cursor: pointer;
}
.section-shop .cart-item:not(:last-child) {
  padding-bottom: 25px;
  border-bottom: 1px solid #e5dccf;
}
.section-shop .tf-sidebar .sidebar-item.cart .subtotal {
  background-color: #2d2723;
  padding: 11px 20px 13px;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-bottom: 28px;
}
.section-shop .tf-sidebar .sidebar-item.cart .subtotal .title {
  font-weight: 700;
  color: var(--White);
  letter-spacing: 0.7px;
}
.section-shop .tf-sidebar .sidebar-item.cart .subtotal .title svg {
  width: 17px;
  height: 17px;
  margin-right: 3px;
}
.section-shop .tf-sidebar .sidebar-item.cart .subtotal .price {
  font-size: 17px;
  color: var(--White);
}
.section-shop .tf-sidebar .sidebar-item.cart .bottom {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  row-gap: 15px;
  padding-left: 2px;
  margin-right: -2px;
}
.section-shop .tf-sidebar .sidebar-item.cart .bottom .tf-btn-default {
  font-size: 15px;
  font-weight: bold;
  line-height: 25px;
  gap: 0px;
}
.section-shop .tf-sidebar .sidebar-item.cart .bottom .tf-btn-default i {
  font-size: 18px;
}
.section-shop .tf-sidebar .sidebar-item.categories .content {
  padding: 28px 23px 17px 30px;
}
.section-shop .tf-sidebar .sidebar-item.categories ul li {
  font-size: 17px;
  line-height: 25px;
  margin-bottom: 10px;
  color: var(--text);
  position: relative;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.section-shop .tf-sidebar .sidebar-item.categories ul li::before {
  position: absolute;
  content: "";
  top: 12px;
  left: -1px;
  opacity: 0;
  width: 18px;
  height: 2px;
  background-color: var(--Primary);
  visibility: hidden;
  -webkit-transition: all 0.2s ease;
  -moz-transition: all 0.2s ease;
  -ms-transition: all 0.2s ease;
  -o-transition: all 0.2s ease;
  transition: all 0.2s ease;
}
.section-shop .tf-sidebar .sidebar-item.categories ul li:hover {
  padding-left: 33px;
}
.section-shop .tf-sidebar .sidebar-item.categories ul li:hover::before {
  opacity: 1;
  visibility: visible;
}
.section-shop .tf-sidebar .sidebar-item.filter .content {
  padding: 39px 23px 35px 30px;
}
.section-shop .tf-sidebar .sidebar-item.recent-product .cart-item {
  gap: 19px;
}
.section-shop .tf-sidebar .sidebar-item.recent-product .cart-item span {
  color: #666666;
  text-decoration-line: line-through;
  margin-right: 17px;
}
.section-shop .tf-sidebar .sidebar-item {
  margin-bottom: 48px;
}
.section-shop .tf-sidebar .sidebar-item > .title {
  background-color: var(--Secondary);
  font-weight: 500;
  color: var(--White);
  letter-spacing: 1px;
  padding: 19px 28px 15px;
}
.section-shop .tf-sidebar .sidebar-item > .content {
  background-color: #f9f5ee;
  padding: 35px 23px 29px 30px;
}

/*section-welcome
---------------------------------------------------------------*/
.section-welcome .wrapper {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  gap: 30px;
}
.section-welcome .wrap-image {
  overflow: hidden;
  flex: none;
}
.section-welcome .wrap-image img {
  width: 100%;
  object-fit: cover;
}
.section-welcome .wrap-image.left {
  border-radius: 201.5px 201.5px 0px 0px;
  height: 593px;
  max-width: 403px;
}
.section-welcome .wrap-image.right {
  border-radius: 50%;
  width: 414px;
  height: 414px;
  margin-top: 89px;
}
.section-welcome .content {
  padding-top: 67px;
}
.section-welcome .content .heading-section {
  margin-bottom: 20px;
}
.section-welcome .content .heading-section .sub-title {
  margin-bottom: 8px;
  letter-spacing: 1.8px;
}
.section-welcome .content .heading-section .title {
  margin-bottom: 24px;
  letter-spacing: 2.4px;
}
.section-welcome .content .desc {
  letter-spacing: 0.1px;
  margin-bottom: 42px;
}

/*section-video
---------------------------------------------------------------*/
.section-video {
  height: 506px;
  background-image: url(../images/section/video-page-title.jpg);
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  background-attachment: fixed;
  position: relative;
}
.section-video::after {
  content: "";
  width: 100%;
  height: 100%;
  background-color: #110d0a;
  opacity: 20%;
  left: 0px;
  top: 0px;
  position: absolute;
}
.section-video .widget-video {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.section-video .widget-video .popup-youtube {
  z-index: 1;
  height: 97px;
  width: 97px;
  visibility: visible;
  opacity: 1;
}

/*section-our-menu
---------------------------------------------------------------*/
.section-our-menu {
  position: relative;
}
.section-our-menu::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: url(../images/section/section-our-menu.jpg);
  top: 0px;
  background-repeat: no-repeat;
  z-index: -2;
  background-size: 100% 73%;
}
.section-our-menu .heading-section {
  position: relative;
  z-index: 1;
  text-align: center;
  margin-bottom: 68px;
}
.section-our-menu .our-menu-item .content {
  background-color: var(--White);
  overflow: hidden;
}
.section-our-menu .our-menu-item .content .title a {
  color: var(--text);
}
.section-our-menu .our-menu-item .content .title:hover a {
  color: var(--Primary);
}
.section-our-menu .our-menu-item .content p {
  color: var(--text);
  margin-bottom: 21px;
}
.section-our-menu .our-menu-item .content .icon {
  color: var(--text);
  font-size: 61px;
}
.section-our-menu .item1 {
  position: absolute;
  top: 0;
}

/*section-why
---------------------------------------------------------------*/
.section-why {
  position: relative;
}
.section-why .wrap-image {
  position: relative;
}
.section-why .wrap-image .image {
  max-width: 503px;
  overflow: hidden;
  margin: 0 auto;
}
.section-why .wrap-image .image img {
  object-fit: cover;
}
.section-why .wrap-image .item1 {
  position: absolute;
  bottom: 63px;
  left: -174px;
}
.section-why .content {
  padding-top: 98px;
}
.section-why .content .heading-section {
  margin-bottom: 30px;
}
.section-why .content .heading-section .title {
  margin-bottom: 27px;
}
.section-why .counter {
  margin-top: 96px;
}
.section-why .item2 {
  position: absolute;
  bottom: 294px;
  right: 0;
  z-index: -1;
}
.section-why.page-about .widget-video {
  background-image: url(../images/courses/course-2.jpg);
  max-width: 403px;
  height: 593px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-repeat: no-repeat;
  background-size: cover;
  border-radius: 201.5px 201.5px 0 0;
  margin-bottom: 0;
}
.section-why.page-about .content {
  margin-left: -13px;
  padding-top: 80px;
}
.section-why.page-about .content .heading-section {
  margin-bottom: 27px;
}
.section-why.page-about .content .heading-section .sub-title {
  margin-bottom: 8px;
}
.section-why.page-about .content .heading-section .title {
  margin-bottom: 24px;
}
.section-why.page-about .content .heading-section .paragraph-1 {
  margin-bottom: 15px;
}
.section-why.page-about .counter {
  margin-top: 136px;
}

/*section-post
---------------------------------------------------------------*/
.section-post {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0 0;
}
.section-post .wrap-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.section-post .box-post {
  background-image: url(../images/background/bg-section-testimonial.jpg);
  background-repeat: no-repeat;
  background-size: cover;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  text-align: center;
  justify-content: center;
}
.section-post .box-post .wrap-content {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.section-post .box-post .content .icon {
  width: 124px;
  height: 20px;
  margin: 0 auto;
  margin-bottom: 40px;
}
.section-post .box-post .content .title {
  font-weight: 700;
  color: var(--text);
  margin-bottom: 28px;
  letter-spacing: 1.5px;
  padding: 0 10px;
}
.section-post .box-post .content p {
  margin-bottom: 41px;
  color: var(--Text-top-bar);
  letter-spacing: 0.2px;
  padding-left: 15px;
  padding-right: 15px;
}
.section-post .box-post .content .decs {
  font-size: 25px;
  line-height: 36px;
  font-weight: 700;
  font-style: italic;
  font-family: "CormorantInfant", sans-serif;
  color: var(--text);
}
.section-post .box-post .content .tf-btn {
  margin: 0 auto;
}
.section-post .box-post.default {
  padding: 145px 0 148px;
}
.section-post .box-post.style-2, .section-post .box-post.style-3 {
  background-image: unset;
  display: block;
}
.section-post .box-post.style-2 .wrap-content, .section-post .box-post.style-3 .wrap-content {
  height: 100%;
}
.section-post .box-post.style-2 .wrap-content .wrap-image img, .section-post .box-post.style-3 .wrap-content .wrap-image img {
  height: 266px;
  width: 100%;
}
.section-post .box-post.style-2 .wrap-content .content .title, .section-post .box-post.style-3 .wrap-content .content .title {
  margin-bottom: 20px;
}
.section-post .box-post.style-2 .wrap-content .content p, .section-post .box-post.style-3 .wrap-content .content p {
  margin-bottom: 43px;
}
.section-post .box-post.style-2 .wrap-content .content .tf-btn, .section-post .box-post.style-3 .wrap-content .content .tf-btn {
  border: 1px solid var(--Secondary);
}
.section-post .box-post.style-2 {
  background-image: unset;
  background-color: var(--Secondary);
}
.section-post .box-post.style-2 .wrap-content {
  padding-bottom: 76px;
}
.section-post .box-post.style-2 .wrap-content .wrap-image {
  margin-bottom: 66px;
}
.section-post .box-post.style-2 .wrap-content .content .title {
  color: var(--White);
}
.section-post .box-post.style-2 .wrap-content .content .tf-btn {
  border: 1px solid var(--Primary);
}
.section-post .box-post.style-3 {
  background-color: var(--Primary);
}
.section-post .box-post.style-3 .wrap-content .content {
  margin: 66px 0 76px;
}
.section-post .box-post.style-3 .wrap-content .content .title {
  color: var(--White);
}

/*section-best-selling
---------------------------------------------------------------*/
.section-best-selling {
  position: relative;
}
.section-best-selling .item1 {
  position: absolute;
  right: 0;
  bottom: 155px;
  z-index: -1;
}
.section-best-selling .item2 {
  position: absolute;
  left: 0;
  top: 150px;
  z-index: -1;
}
.section-best-selling .heading-section {
  text-align: center;
  margin-bottom: 89px;
}
.section-best-selling .heading-section .title {
  margin-bottom: 15px;
}
.section-best-selling .product-item {
  margin-bottom: 60px;
}
.section-best-selling .tf-btn {
  margin: 0 auto;
  margin-top: 7px;
}

/*section-testimonial
---------------------------------------------------------------*/
.section-testimonial {
  text-align: center;
  position: relative;
}
.section-testimonial::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: url(../images/background/bg-section-testimonial.jpg);
  top: 0px;
  left: 0;
  background-repeat: no-repeat;
  z-index: -2;
  background-size: 100% 68.1%;
}
.section-testimonial .swiper-container {
  margin-bottom: 126px;
}
.section-testimonial .wrap-service-item {
  padding: 0 30px;
}
.section-testimonial.page-about .swiper-container {
  margin-bottom: 0;
}
.section-testimonial.page-about::before {
  display: none;
}
.section-testimonial.page-about .testimonial-item .icon {
  margin-bottom: 43px;
}
.section-testimonial.page-about .testimonial-item .paragraph {
  margin-bottom: 27px;
}
.section-testimonial.page-about .testimonial-item .rating {
  margin-bottom: 26px;
}

/*section-location
---------------------------------------------------------------*/
.section-location .wrap-image {
  margin-top: 57px;
  max-height: 409px;
  max-width: 409px;
  border-radius: 50%;
  overflow: hidden;
}
.section-location .wrap-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.section-location .content {
  text-align: center;
  margin: 0px -33px;
  margin-bottom: 42px;
}
.section-location .content .heading-section {
  margin-bottom: 35px;
}
.section-location .content .heading-section .sub-title {
  margin-bottom: 18px;
}
.section-location .content .heading-section .title {
  margin-bottom: 15px;
}
.section-location .content h5 {
  color: var(--text);
  font-weight: 700;
  letter-spacing: 1px;
  margin-bottom: 10px;
}
.section-location .content .number {
  color: var(--Primary);
  font-family: "CormorantInfant", sans-serif;
  font-weight: 700;
  margin-bottom: 20px;
  letter-spacing: 1px;
}
.section-location .content .infor {
  color: var(--text);
  letter-spacing: 0.4px;
  margin-bottom: 5px;
}
.section-location .tf-btn {
  margin: 0 auto;
}
.section-location .map-box {
  margin-top: 57px;
  margin-left: auto;
}
.section-location.page-contact-us {
  background-image: url(../images/background/bg-section-testimonial.jpg);
  background-size: cover;
  background-repeat: no-repeat;
}
.section-location.page-contact-us .content {
  margin-bottom: 48px;
}
.section-location.page-contact-us .tf-social-icon {
  justify-content: center;
  gap: 12px;
}
.section-location.page-contact-us .tf-social-icon li {
  height: 45px;
  width: 45px;
  background-color: var(--White);
}
.section-location.page-contact-us .tf-social-icon li:hover {
  background-color: var(--Primary);
}

/*section-follow
---------------------------------------------------------------*/
.section-follow .swiper-container {
  margin-bottom: 32px;
}
.section-follow p {
  color: #2d2723;
  font-weight: 600;
  text-align: center;
  letter-spacing: 2.2px;
}
.section-follow p i {
  padding-right: 8px;
}
.section-follow.page-about .grid-layout-5 {
  margin-bottom: 33px;
}

/*section-portfolio
---------------------------------------------------------------*/
.section-portfolio .widget-menu-tab {
  margin-bottom: 65px;
  grid-template-columns: repeat(3, 1fr);
}
.section-portfolio .widget-content-tab .tf-btn {
  margin: 0 auto;
  grid-template-columns: repeat(3, 1fr);
}
.section-portfolio .widget-content-tab .grid-layout-3 {
  grid-template-columns: repeat(3, 1fr);
  margin-bottom: 67px;
}
.section-portfolio .showmore-item .fl-item {
  display: none;
  
}
.section-portfolio .showmore-item1 .fl-item1 {
  display: none;
}
.section-portfolio .showmore-item2 .fl-item2 {
  display: none;
}
.section-portfolio .showmore-item3 .fl-item3 {
  display: none;
}
.section-portfolio .showmore-item4 .fl-item4 {
  display: none;
}

/*section-send
---------------------------------------------------------------*/
.section-send {
  position: relative;
}
.section-send .heading-section {
  text-align: center;
  margin-bottom: 67px;
}
.section-send .heading-section .title {
  margin-bottom: 16px;
}
.section-send .box-send .cols {
  margin-bottom: 20px;
}
.section-send .box-send form textarea {
  height: 250px;
}
.section-send .box-send form .tf-btn {
  margin-top: 55px;
}
.section-send .box-send form .tf-btn span {
  gap: 10px;
}
.section-send .item1 {
  position: absolute;
  bottom: 28px;
}
.section-send .item2 {
  position: absolute;
  right: 0;
  bottom: 27px;
}

/*section-our-product
---------------------------------------------------------------*/
.section-tabs-decription p {
  letter-spacing: 0.4px;
}

/*section-our-product
---------------------------------------------------------------*/
.section-our-product .heading-section {
  text-align: center;
  margin-bottom: 64px;
}
.section-our-product .heading-section .sub-title {
  margin-bottom: 18px;
}
.section-our-product .heading-section .title {
  margin-bottom: 15px;
}
.section-our-product .heading-section p {
  letter-spacing: 0.1px;
}
.section-our-product.page-our-menu {
  background-image: url(../images/background/bg-blog-2.jpg);
  background-repeat: no-repeat;
  position: relative;
  background-size: 100% 77%;
}
.section-our-product.page-our-menu .our-menu-item .content {
  top: 34px;
  left: 35px;
  right: 35px;
  bottom: 33px;
}
.section-our-product.page-our-menu .our-menu-item .content .wrap {
  padding: 53px 0 0 0;
}
.section-our-product.page-our-menu .our-menu-item .content .title {
  margin-bottom: 14px;
}
.section-our-product.page-our-menu .item1 {
  position: absolute;
  top: 0;
  left: -125px;
}

/*section-menu
---------------------------------------------------------------*/
.section-menu .wrap-menu-item {
  margin-left: 15px;
}
.section-menu .wrap-menu-item .wg-menu-item {
  margin-bottom: 36px;
}

/*section-order
---------------------------------------------------------------*/
.section-order {
  background-image: url(../images/background/our-menu.jpg);
  position: relative;
  background-repeat: no-repeat;
  background-size: cover;
}
.section-order::after {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  background-color: rgb(18, 15, 12);
  opacity: 0.502;
  top: 0;
  z-index: 1;
}
.section-order .content {
  position: relative;
  z-index: 2;
  text-align: center;
  padding-top: 74px;
  padding-bottom: 76px;
}
.section-order .content .logo {
  height: 120px;
  width: 118px;
  margin: 0 auto;
  margin-bottom: 29px;
}
.section-order .content .logo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.section-order .content .title {
  font-weight: 700;
  color: var(--White);
  font-family: "CormorantInfant", sans-serif;
  letter-spacing: 1.9px;
  margin-bottom: 17px;
}
.section-order .content p {
  font-size: 17px;
  color: var(--White);
  margin-bottom: 42px;
}
.section-order .content .tf-btn {
  margin: 0 auto;
}

/*section-ourt-team
---------------------------------------------------------------*/
.section-our-team .heading-section {
  margin-bottom: 65px;
  text-align: center;
}
.section-our-team .heading-section .icon {
  width: 124px;
  height: 20px;
  margin: 0 auto;
  margin-bottom: 39px;
}
.section-our-team .heading-section .short-desc {
  padding: 0 8px;
  line-height: 35px;
  letter-spacing: 0.3px;
  color: var(--text-2);
}
.section-our-team .grid-layout-3 {
  gap: 76px 30px;
}

/*section-job
---------------------------------------------------------------*/
.section-job {
  position: relative;
}
.section-job .content {
  position: relative;
  z-index: 2;
}
.section-job .content .heading-section {
  margin-bottom: 29px;
}
.section-job .content .heading-section .title {
  margin-bottom: 25px;
}
.section-job .content .list-check {
  margin-bottom: 47px;
}
.section-job .content .list-check li {
  font-family: "CormorantInfant", sans-serif;
  gap: 24px;
  color: var(--text-2);
  font-weight: 500;
}
.section-job .item {
  position: absolute;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.wrap-section {
  background-image: url(../images/background/bg-section-testimonial.jpg);
  background-size: cover;
}

.intro {
  text-align: center;
  margin-bottom: 125px;
}
.intro p {
  font-family: "CormorantInfant", sans-serif;
  color: var(--text);
  font-weight: 700;
  font-style: italic;
  line-height: 33px;
  letter-spacing: 0.2px;
}

/*partners
--------------------------------------------------------------*/
/* preload */
/* -------------------------------- */
.preload-container {
  position: fixed;
  top: 0px;
  left: 0px;
  z-index: 999999999999999;
  height: 100%;
  width: 100%;
  background-color: #000;
}
.preload-container .loader {
  z-index: 100;
  top: 50%;
  left: 50%;
  position: absolute;
}
.preload-container .loader span {
  position: absolute;
  color: #fff;
  transform: translate(-50%, -50%);
  font-size: 38px;
  letter-spacing: 5px;
}
.preload-container .loader span:nth-child(1) {
  color: transparent;
  -webkit-text-stroke: 0.3px var(--Primary);
}
.preload-container .loader span:nth-child(2) {
  color: var(--Primary);
  -webkit-text-stroke: 1px var(--Primary);
  animation: tf-preload 3s ease-in-out infinite;
}

@keyframes tf-preload {
  0%, 100% {
    clip-path: polygon(0% 45%, 15% 44%, 32% 50%, 54% 60%, 70% 61%, 84% 59%, 100% 52%, 100% 100%, 0% 100%);
  }
  50% {
    clip-path: polygon(0% 60%, 16% 65%, 34% 66%, 51% 62%, 67% 50%, 84% 45%, 100% 46%, 100% 100%, 0% 100%);
  }
}
/*-------------- Responsive ----------------- */
@media (min-width: 1490px) {
  .section-welcome .wrapper {
    gap: 80px;
  }
}
@media (min-width: 1441px) {
  .section-post .box-post .content p {
    padding: 0 75px;
  }
}
@media (min-width: 1205px) {
  .history-main .history-box .content {
    margin-right: 73px;
  }
  .history-main .history-box.style-2 .content {
    margin-left: 100px;
  }
}
@media (min-width: 1200px) {
  .history-main .history-box .content {
    margin-right: 73px;
  }
}
@media (min-width: 1150px) {
  .tf-dropdown-sort {
    padding: 10px 18px;
    min-width: 193px;
  }
}
/*max-width
-------------------------------------------------------------------*/
@media (max-width: 1441px) {
  #header-main .header-inner-wrap .header-left {
    gap: 30px;
  }
  #header-main .header-inner-wrap .header-right {
    gap: 30px;
  }
}
@media (max-width: 1400px) {
  .section-our-menu::after {
    width: 400px;
    height: 400px;
    background-size: cover;
  }
}
@media (max-width: 1300px) {
  .section-welcome .wrapper {
    gap: 30px;
  }
  .section-welcome .wrapper .wrap-image.left {
    height: 498px;
    max-width: 329px;
  }
  .section-welcome .wrapper .wrap-image.right {
    height: 350px;
    width: 350px;
  }
}
@media (max-width: 1200px) {
  #header-main .header-inner-wrap .header-left .navigation {
    gap: 20px;
  }
  #header-main .header-inner-wrap .header-right {
    gap: 20px;
  }
  #header-main .header-inner-wrap .header-right .header-contact {
    gap: 15px;
  }
  .history-main .history-box .wrap-image,
  .history-main .history-box .content {
    width: 41%;
  }
  .section-post {
    flex-wrap: wrap;
  }
  .section-testimonial .swiper-container .swiper-button-next,
  .section-testimonial .swiper-container .swiper-button-prev {
    display: none;
  }
  .testimonial-item .paragraph {
    padding: 15px;
  }
  .section-welcome .wrapper {
    flex-wrap: wrap;
    justify-content: center;
  }
  .section-welcome .wrapper .wrap-image.left {
    height: unset;
    max-width: unset;
    flex: unset;
  }
  .section-welcome .wrapper .wrap-image.right {
    max-width: 414px;
    max-height: 414px;
    height: unset;
    width: unset;
    flex: unset;
  }
  .section-welcome .wrapper .content {
    text-align: center;
  }
  .section-welcome .wrapper .content .tf-btn {
    margin: 0 auto;
  }
}
@media (max-width: 1150px) {
  .section-location .wrap-image {
    margin: 0 auto;
    margin-bottom: 30px;
  }
  .section-location .content {
    margin: 0 0px 20px;
  }
  .section-location .map-box {
    margin: 30px auto 0;
  }
  .service-item .content {
    padding-left: 15px;
    padding-right: 15px;
  }
  .service-item .content h2 {
    font-size: 25px !important;
  }
}
@media (max-width: 1024px) {
  #header-main .header-inner-wrap .header-right .header-contact {
    display: none;
  }
  .section-post {
    grid-template-columns: repeat(2, 1fr);
  }
  .section-post .box-post.default {
    padding-top: 66px;
    padding-bottom: 66px;
  }
  .service-item .content {
    padding-bottom: 50px;
  }
}
@media (max-width: 991px) {
  br {
    display: none;
  }
  .item1,
  .item2 {
    display: none !important;
  }
  .grid-layout-3 {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px 20px !important;
  }
  #header-main .header-inner-wrap .header-left {
    gap: 20px;
  }
  #header-main .header-inner-wrap .header-left .navigation {
    display: none;
  }
  #header-main .header-inner-wrap .header-right .mobile-button {
    display: block;
  }
  .page-title-home .swiper-container .swiper-button-prev {
    display: none !important;
  }
  .page-title-home .swiper-container .swiper-button-next {
    display: none !important;
  }
  .article-blog-item {
    margin-bottom: 30px;
  }
  .tf-product-media-wrap .image-top img {
    width: 100%;
  }
  .section-our-menu::after {
    display: none;
  }
  .section-location .content {
    margin-right: 0;
    margin-left: 0;
  }
  .section-welcome .wrapper {
    gap: 30px;
  }
  .section-welcome .wrapper .content {
    padding-top: 0;
  }
  .section-welcome .wrapper .wrap-image.right {
    margin-top: 0;
  }
  .section-best-selling .item1,
  .section-best-selling .item2 {
    display: none;
  }
  .section-icon-box .icon-box {
    margin-bottom: 30px;
  }
  .section-icon-box .icon-box::after {
    display: none;
  }
  .section-why .content {
    padding-top: 45px;
  }
  .section-why.page-about .widget-video {
    margin: 0 auto;
  }
  .section-why.page-about .content {
    margin-left: 0;
  }
  .shop-detail .tf-product-media-wrap .image-bottom {
    justify-content: start;
  }
  .section-sign-up .wrap-sign-up .title,
  .section-sign-up .wrap-sign-up .text-paragraph,
  .section-sign-up .wrap-sign-up .form-send-email {
    padding: 0 15px;
  }
  .history-main::before {
    margin: 0 0 0 30px;
  }
  .history-main .history-box {
    flex-direction: column;
    align-items: flex-start;
    margin-left: 125px;
    margin-bottom: 70px !important;
  }
  .history-main .history-box::before {
    left: -95px !important;
  }
  .history-main .history-box::after {
    left: -95px !important;
  }
  .history-main .history-box .wrap-image {
    width: 100%;
    margin: 30px 0 30px;
  }
  .history-main .history-box .content {
    width: 100%;
    padding-top: 0 !important;
    margin: 0 0 0 !important;
  }
  .section-menu .wrap-menu-item {
    margin-left: 0;
  }
}
@media (max-width: 820px) {
  .cart-item {
    flex-wrap: wrap;
    row-gap: 0px;
  }
}
@media (max-width: 768px) {
  h1 {
    font-size: 45px !important;
    line-height: 50px !important;
  }
  h2 {
    font-size: 30px !important;
    line-height: 30px !important;
  }
  .text-4 {
    font-size: 35px;
    line-height: 45px;
  }
  .main-content {
    padding-top: 50px !important;
    padding-bottom: 50px !important;
  }
  .tf-spacing-1,
  .tf-spacing-2,
  .tf-spacing-3,
  .tf-spacing-4,
  .tf-spacing-5,
  .tf-spacing-6,
  .tf-spacing-7,
  .tf-spacing-8,
  .tf-spacing-9,
  .tf-spacing-10,
  .tf-spacing-11,
  .tf-spacing-12,
  .tf-spacing-13,
  .tf-spacing-14,
  .tf-spacing-15,
  .tf-spacing-16,
  .tf-spacing-17 {
    padding-top: 50px;
    padding-bottom: 50px;
  }
  .heading-section {
    margin-bottom: 10px !important;
  }
  .heading-section .sub-title,
  .heading-section p,
  .heading-section .title {
    margin-bottom: 5px !important;
  }
  .grid-layout-3 {
    grid-template-columns: repeat(1, 1fr);
  }
  .page-title {
    padding-top: 160px;
    padding-bottom: 100px;
    background-attachment: unset !important;
  }
  .page-title .content .under-line {
    margin-bottom: 40px;
  }
  .page-title.style-2 {
    padding-top: 160px;
    padding-bottom: 160px;
  }
  #footer .wrap-footer .footer-body {
    padding-top: 60px;
  }
  #footer .wrap-footer .footer-bottom {
    justify-content: center;
  }
  .section-blog-grid {
    padding-top: 20px;
  }
  .section-blog-grid .article-blog-item .image img {
    height: auto;
  }
  .widget-video {
    margin-bottom: 30px;
  }
  .section-welcome .wrapper .content .desc {
    margin-bottom: 20px;
  }
  .history-main {
    padding-bottom: 70px;
  }
  .history-main .history-box::before {
    width: 30px;
  }
  .product-item {
    margin-bottom: 30px;
  }
  .service-item .content .icon,
  .service-item .content h2,
  .service-item .content p {
    margin-bottom: 20px;
  }
  .section-why .content {
    padding-top: 0;
  }
  .section-why .counter {
    margin-top: 30px !important;
    margin-bottom: -30px !important;
  }
  .section-why.page-about .content {
    padding-top: 30px;
  }
  .section-testimonial .swiper-container {
    margin-bottom: 40px;
  }
  .intro {
    margin-bottom: 30px;
  }
  .icon-box .icon {
    margin-bottom: 10px;
  }
  .portfolio-item .wrap-image {
    margin-bottom: 15px;
  }
  .section-shop .tf-sidebar {
    margin-top: 30px;
  }
  .product-item {
    margin-bottom: 40px;
  }
  .product-item .wrap-image {
    margin-bottom: 15px;
  }
  .counter .number-counter {
    margin-bottom: 30px;
  }
  .counter .number-counter::after {
    display: none;
  }
  .testimonial-item .icon {
    margin-bottom: 10px;
  }
  .testimonial-item .paragraph {
    margin-bottom: 15px;
  }
}
@media (max-width: 757px) {
  .widget-tabs .widget-menu-tab {
    justify-content: unset;
    padding-bottom: 10px;
    margin-bottom: 20px;
  }
}
@media (max-width: 700px) {
  .reservations-location .map-box {
    width: 345px;
    height: 345px;
    margin-bottom: 30px;
  }
  .section-follow .wrap-follow-item {
    flex-wrap: wrap;
  }
  .wg-404 .content .title::before {
    transform: translateX(-175px);
    top: 0;
    font-size: 165px;
  }
  .wg-404 .content .title img {
    width: 200px;
  }
  .wg-404 .content .title::after {
    transform: translateX(175px);
    top: 0;
    font-size: 165px;
  }
  .testimonial-item .paragraph {
    font-size: 22px;
  }
}
@media (max-width: 690px) {
  .section-follow .wrap-follow-item .follow-item {
    width: 48%;
  }
}
@media (max-width: 575px) {
  .section-why .wrap-image .image {
    max-width: 100%;
  }
  .section-why .content {
    padding-top: 30px;
  }
}
@media (max-width: 570px) {
  .shop-detail .content-inner {
    padding-left: 0;
  }
}
@media (max-width: 550px) {
  .text-4 {
    font-size: 30px;
    line-height: 35px;
  }
  .tf-top-bar {
    text-align: center;
  }
  .tf-top-bar .wrap-top {
    flex-direction: column;
    align-items: center;
  }
  .tf-top-bar .wrap-top .list-info {
    justify-content: center;
  }
  .tf-top-bar .wrap-top .list-info li {
    align-items: flex-start;
  }
  #header-main .header-inner-wrap .header-right .wrap-header-icons {
    gap: 40px;
  }
  #header-main .header-inner-wrap .header-right .wrap-header-icons .header-cart::before {
    left: -20px;
  }
  .counter .number-counter .counter-content {
    font-size: 60px;
  }
  .section-post {
    grid-template-columns: repeat(1, 1fr);
  }
  .service-item .content {
    padding-bottom: 30px;
  }
  .grid-layout-5 {
    grid-template-columns: repeat(1, 1fr);
  }
  .section-follow .wrap-follow-item .follow-item {
    width: 100%;
    height: auto;
  }
  .map-box {
    height: 300px;
    width: 300px;
  }
  .history-main .history-box {
    margin-left: 80px;
    margin-bottom: 50px !important;
  }
  .history-main .history-box::before, .history-main .history-box::after {
    left: -50px !important;
  }
  .history-main .history-box .wrap-image .item-history2 {
    width: 40%;
    right: -11px !important;
  }
  .history-main .history-box .wrap-image .item-history3 {
    width: 60%;
  }
  .our-menu-item .content {
    padding: 0;
  }
  .our-menu-item .content p {
    margin-bottom: 0 !important;
  }
  .service-item .content .icon,
  .service-item .content h2,
  .service-item .content p {
    margin-bottom: 4px;
  }
  .section-location .content h5 {
    margin-bottom: 2px;
  }
  .section-location .content .number {
    margin-bottom: 8px;
  }
}
@media (max-width: 375px) {
  .our-menu-item .content .wrap {
    padding: 40px 5px 0;
  }
  .our-menu-item .content .wrap .title {
    margin-bottom: 2px;
  }
  .section-our-product.page-our-menu .our-menu-item .content .wrap {
    padding: 27px 0px 0;
  }
  .section-our-product.page-our-menu .our-menu-item .content .wrap .price {
    margin-bottom: 4px;
  }
  .section-our-product.page-our-menu .our-menu-item .content .wrap .title {
    margin-bottom: 3px;
  }
}

/*# sourceMappingURL=styles.css.map */
